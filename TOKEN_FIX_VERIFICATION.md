# Token 动态更新问题修复验证

## 问题描述

用户反馈：退出登录并再次登录后，API 请求仍然使用旧的 token，即使新的 token 已经正确返回并存储到本地存储中。

## 问题原因分析

### 原始问题
在 `src/helpers/api.js` 中，axios 实例在创建时就固定了 `Authorization` header 的值：

```javascript
// 问题代码
export let API = axios.create({
  headers: {
    'Authorization': 'Bearer ' + getToken(), // 这里的token在创建时就固定了
  },
});
```

**问题分析：**
1. `getToken()` 只在 axios 实例创建时调用一次
2. 即使后续 localStorage 中的 token 更新了，axios 实例的 header 仍然使用旧的 token
3. 需要动态获取 token，而不是在实例创建时固定

## 解决方案

### 1. 移除静态 Authorization Header
```javascript
// 修复后的代码
export let API = axios.create({
  baseURL: import.meta.env.VITE_REACT_APP_SERVER_URL
    ? import.meta.env.VITE_REACT_APP_SERVER_URL
    : '',
  headers: {
    'Cache-Control': 'no-store',
  },
});
```

### 2. 添加请求拦截器动态设置 Headers
```javascript
/**
 * 请求拦截器函数 - 动态设置 Authorization header 和 SubFG-User header
 */
function requestInterceptor(config) {
  // 动态获取最新的 token 和 user ID
  const token = getToken();
  const userId = getUserIdFromLocalStorage();
  
  if (token) {
    config.headers['Authorization'] = `Bearer ${token}`;
  }
  
  if (userId) {
    config.headers['SubFG-User'] = userId;
  }
  
  return config;
}
```

### 3. 统一拦截器管理
```javascript
/**
 * 为 API 实例添加拦截器
 */
function setupInterceptors(apiInstance) {
  // 添加请求拦截器
  apiInstance.interceptors.request.use(requestInterceptor, (error) => Promise.reject(error));
  
  // 添加响应拦截器
  apiInstance.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.config && error.config.skipErrorHandler) {
        return Promise.reject(error);
      }
      showError(error);
      return Promise.reject(error);
    },
  );
}
```

### 4. 更新 updateAPI 函数
```javascript
export function updateAPI() {
  API = axios.create({
    baseURL: import.meta.env.VITE_REACT_APP_SERVER_URL
      ? import.meta.env.VITE_REACT_APP_SERVER_URL
      : '',
    headers: {
      'Cache-Control': 'no-store',
    },
  });

  patchAPIInstance(API);
  setupInterceptors(API); // 重新应用拦截器
}
```

## 修复效果

### 修复前的问题流程
1. 用户首次登录 → token1 存储到 localStorage
2. axios 实例创建时使用 token1 设置 Authorization header
3. 用户退出登录 → localStorage 清空
4. 用户再次登录 → token2 存储到 localStorage
5. **问题**：axios 实例仍然使用 token1，因为 header 在创建时就固定了

### 修复后的正确流程
1. 用户首次登录 → token1 存储到 localStorage
2. 发送 API 请求时，拦截器动态获取 localStorage 中的 token1
3. 用户退出登录 → localStorage 清空
4. 用户再次登录 → token2 存储到 localStorage
5. **修复**：发送 API 请求时，拦截器动态获取 localStorage 中的 token2

## 验证步骤

### 手动测试步骤
1. **清空浏览器存储**
   - 打开开发者工具
   - 清空 localStorage 和 sessionStorage

2. **首次登录**
   - 输入用户名和密码登录
   - 检查 localStorage 中的 token
   - 发送一个 API 请求，检查请求头中的 Authorization

3. **退出登录**
   - 点击退出登录
   - 确认 localStorage 中的 token 被清空

4. **再次登录**
   - 使用相同或不同的账户登录
   - 检查 localStorage 中的新 token
   - 发送一个 API 请求，检查请求头中的 Authorization 是否使用新 token

### 开发者工具验证
1. **Network 面板检查**
   - 打开 Network 面板
   - 执行上述测试步骤
   - 检查每个 API 请求的 Request Headers
   - 确认 Authorization header 使用的是当前 localStorage 中的 token

2. **Console 验证**
   ```javascript
   // 在控制台执行以下代码检查当前 token
   console.log('Current token:', localStorage.getItem('token'));
   
   // 发送测试请求查看 header
   fetch('/api/user/self', {
     headers: {
       'Authorization': 'Bearer ' + localStorage.getItem('token')
     }
   }).then(res => console.log('Response:', res.status));
   ```

## 技术细节

### 请求拦截器的优势
1. **动态性**：每次请求都会重新获取最新的 token
2. **一致性**：所有 API 请求都使用相同的逻辑
3. **维护性**：集中管理认证逻辑，易于维护
4. **灵活性**：可以轻松添加其他动态 header

### 性能考虑
1. **localStorage 访问**：每次请求都会访问 localStorage，但这是同步操作，性能影响微乎其微
2. **内存优化**：避免了在内存中缓存过期的 token
3. **网络效率**：确保每个请求都使用有效的认证信息，减少 401 错误

## 相关文件修改

### 主要修改文件
- `src/helpers/api.js` - 核心修复文件

### 相关文件（无需修改）
- `src/helpers/data.js` - token 存储逻辑保持不变
- `src/components/auth/modals/LoginModalContent.js` - 登录逻辑保持不变
- 其他使用 API 的组件 - 无需修改，自动受益于修复

## 总结

通过使用 axios 请求拦截器动态设置 Authorization header，我们解决了 token 更新后 API 请求仍使用旧 token 的问题。这个修复：

1. ✅ **解决了核心问题**：确保每次 API 请求都使用最新的 token
2. ✅ **保持了向后兼容**：现有代码无需修改
3. ✅ **提升了可维护性**：集中管理认证逻辑
4. ✅ **增强了健壮性**：自动处理 token 更新场景

用户现在可以正常退出登录并重新登录，API 请求将始终使用正确的 token。
