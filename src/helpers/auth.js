import React from 'react';
import { Navigate } from 'react-router-dom';
import { history } from './history';

export function authHeader() {
  let user = JSON.parse(localStorage.getItem('user'));

  if (user && user.token) {
    return { Authorization: 'Bearer ' + user.token };
  } else {
    return {};
  }
}

export const AuthRedirect = ({ children }) => {
  const user = localStorage.getItem('user');
  // 从哪里跳转来，就从哪里跳转回去
  const from = history.location.state?.from?.pathname || '/';
  if (user) { // 如果用户存在，重定向到控制台
    return <Navigate to={from} replace />; // 重定向到首页
  }

  return children;
};

/**
 * 私有路由组件
 * 检查用户是否已登录，如果未登录则通过全局处理器显示登录Modal
 */
function PrivateRoute({ children }) {
  const isAuthenticated = localStorage.getItem('token');

  React.useEffect(() => {
    if (!isAuthenticated) {

      // 使用全局认证处理器显示登录Modal
      if (window.__globalAuthModalHandler) {
        window.__globalAuthModalHandler(window.location.pathname);
      } else {
        // 如果全局处理器还没有设置，延迟重试
        setTimeout(() => {
          if (window.__globalAuthModalHandler) {
            window.__globalAuthModalHandler(window.location.pathname);
          }
        }, 100);
      }
    }
  }, [isAuthenticated]);

  return children;
}

export { PrivateRoute };

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
export const isLoggedIn = () => {
  const token = localStorage.getItem('token');
  const user = localStorage.getItem('user');
  return !!(token && user);
};

/**
 * 获取当前用户信息
 * @returns {Object|null} 用户信息对象或null
 */
export const getCurrentUser = () => {
  try {
    const user = localStorage.getItem('user');
    return user ? JSON.parse(user) : null;
  } catch (error) {
    console.error('Failed to parse user data:', error);
    return null;
  }
};

/**
 * 获取当前访问令牌
 * @returns {string|null} 访问令牌或null
 */
export const getToken = () => {
  return localStorage.getItem('token');
};

/**
 * 清除认证信息
 */
export const clearAuthData = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('user');
};

/**
 * 需要登录时的处理函数
 * 这个函数可以在需要登录的地方调用，它会触发登录Modal
 * @param {Function} showLoginModal - 显示登录Modal的函数
 * @param {string} returnUrl - 登录成功后返回的URL
 * @returns {boolean} 如果用户已登录返回true，否则返回false
 */
export const requireAuth = (showLoginModal, returnUrl = null) => {
  if (isLoggedIn()) {
    return true;
  }

  if (showLoginModal) {
    showLoginModal(returnUrl || window.location.pathname);
  }

  return false;
};
