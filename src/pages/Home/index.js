import React, { useContext, useEffect, useState } from 'react';
import { Typography } from '@douyinfe/semi-ui';
import { API, showError } from '../../helpers';
import { useIsMobile } from '../../hooks/useIsMobile.js';
import { useTranslation } from 'react-i18next';
import NoticeModal from '../../components/layout/NoticeModal';
const { Text } = Typography;
const Home = () => {
  const { t, i18n } = useTranslation();
  const [noticeVisible, setNoticeVisible] = useState(false);
  const isMobile = useIsMobile();
  const isChinese = i18n.language.startsWith('zh');
  const [productList, setProductList] = useState([]);
  const getProductList = async () => {
    try {
      const res = await API.get('/api/v3/product/page', {
        params: {
          pageNum: 1,
          pageSize: 20
        }
      });
      const { success = res.data.code == 200, data } = res.data;
      if (success) {
        setProductList(data.records);

      }
    } catch (error) {
      showError(t(error.response.data.message));
    }
  };

  useEffect(() => {
    getProductList();
  }, []);

  return (
    <div className="w-full overflow-x-hidden">
      <NoticeModal
        visible={noticeVisible}
        onClose={() => setNoticeVisible(false)}
        isMobile={isMobile}
      />
      {(
        <div className="w-full overflow-x-hidden">
          {/* Banner 部分 */}
          <div className="w-full border-b border-semi-color-border min-h-[500px] md:min-h-[600px] lg:min-h-[700px] relative overflow-x-hidden">
            {/* 背景模糊晕染球 */}
            <div className="blur-ball blur-ball-indigo" />
            <div className="blur-ball blur-ball-teal" />
            <div className="flex items-center justify-center h-full px-4 py-20 md:py-24 lg:py-32 mt-10">
              {/* 居中内容区 */}
              <div className="flex flex-col items-center justify-center text-center max-w-6xl mx-auto">
                <div className="flex flex-col items-center justify-center mb-6 md:mb-8">
                  <h1 className={`text-3xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-semi-color-text-0 leading-tight ${isChinese ? 'tracking-wide md:tracking-wider' : ''}`}>
                    {i18n.language === 'en' ? (
                      <>
                        SubFG Creates A Mutual  <br />
                        <span className="shine-text"> Benefit Subscription Community</span>
                      </>
                    ) : (
                      <>
                        SubFG构建<br />
                        <span className="shine-text">互惠订阅社区，优化订阅体验</span>
                      </>
                    )}
                  </h1>
                  <p className="text-base sm:text-md md:text-lg lg:text-xl text-semi-color-text-1 mt-4 md:mt-6 max-w-xl">
                    {t('想要使用优惠套餐找不到一起组队的伙伴？ SubFG帮您解决！')}
                  </p>

                </div>
              </div>

            </div>

            {/* 产品滚动列表 */}
          </div>

        </div>
      )}
    </div>
  );
};

export default Home;

