/* 个人中心页面自定义样式 */

/* 渐变背景动画 */
.profile-gradient-bg {
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

/* 卡片悬停效果 */
.profile-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  
  backdrop-filter: blur(10px);
  border-radius: 16px;
}

.profile-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* 头像发光效果 */
.profile-avatar {
  position: relative;
  overflow: visible;
}

.profile-avatar::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
  border-radius: 50%;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.profile-avatar:hover::before {
  opacity: 0.7;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 在线状态指示器动画 */
.online-indicator {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

/* 功能卡片渐变背景 */
.feature-card-blue {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.feature-card-green {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.feature-card-emerald {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* 按钮悬停效果 */
.profile-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.profile-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.profile-button:hover::before {
  left: 100%;
}

/* 统计卡片动画 */
.stats-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stats-card:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.stats-card:hover .stats-icon {
  transform: scale(1.1) rotate(5deg);
}

.stats-icon {
  transition: transform 0.3s ease;
}

/* 选项卡动画 */
.tab-content {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .profile-card:hover {
    transform: none;
  }

  .stats-card:hover {
    transform: none;
  }
}

@media (max-width: 768px) {
  .profile-gradient-bg {
    background-attachment: scroll;
  }

  .profile-card {
    backdrop-filter: none;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .profile-card {
    background: rgba(31, 41, 55, 0.8);
  }

  .feature-card-blue {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.3) 100%);
  }

  .feature-card-green {
    background: linear-gradient(135deg, rgba(240, 147, 251, 0.3) 0%, rgba(245, 87, 108, 0.3) 100%);
  }

  .feature-card-emerald {
    background: linear-gradient(135deg, rgba(79, 172, 254, 0.3) 0%, rgba(0, 242, 254, 0.3) 100%);
  }
}

/* 加载动画 */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

/* 滚动条样式 */
.profile-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.profile-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.profile-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

.profile-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}