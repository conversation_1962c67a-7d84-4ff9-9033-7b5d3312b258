import React, { useContext, useEffect, useState } from 'react';
import {
    API,
    isRoot,
    isAdmin,
    showError,
    showSuccess,
} from '../../helpers';
import { UserContext } from '../../context/User';
import { useAuthModal } from '../../context/Auth';
import {
    Avatar,
    Button,
    Card,
    Tag,
    Typography,
    Tabs,
    TabPane,
} from '@douyinfe/semi-ui';
import {
    IconMail,
    IconLock,
    IconEdit,
    IconMailStroked,
    IconCreditCard
} from '@douyinfe/semi-icons';
import { SiWechat } from 'react-icons/si';
import { UserPlus, ShieldCheck, Shield, Settings, CreditCard } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import './My.css';

const { Title, Text } = Typography;

const My = () => {
    const [userState, userDispatch] = useContext(UserContext);
    const { t } = useTranslation();
    const {
        showChangePasswordModal,
        showChangeEmailModal,
        showEditProfileModal
    } = useAuthModal();
    const [wechatBinding, setWechatBinding] = useState(false);

    useEffect(() => {
        getUserData();
    }, []);

    // 获取用户数据
    const getUserData = async () => {
        try {
            const res = await API.get('/api/v3/user/getUserInfo');
            const { success = res.data.code == 200, data } = res.data;
            if (success) {
                userDispatch({ type: 'login', payload: data });
                setWechatBinding(!!data.wechatUnionid);
            }
        } catch (error) {
            showError(t(error.response?.data?.message || '获取用户信息失败'));
        }
    };



    // 获取头像显示文本
    const getAvatarText = () => {
        const name = userState?.user?.userName || userState?.user?.email || '';
        if (name && name.length > 0) {
            return name.slice(0, 2).toUpperCase();
        }
        return 'NA';
    };

    // 微信绑定/解绑
    const handleWechatBinding = async () => {
        if (wechatBinding) {
            // 解绑微信
            try {
                const res = await API.post('/api/v3/user/unbindThirdPartyAccount', {
                    thirdPartyType: 'wechat'
                });
                const { success = res.data.code == 200 } = res.data;
                if (success) {
                    showSuccess(t('微信解绑成功'));
                    setWechatBinding(false);
                    getUserData(); // 刷新用户数据
                }
            } catch (error) {
                showError(t(error.response?.data?.message || '微信解绑失败'));
            }
        } else {
            // 绑定微信 - 跳转到微信授权页面
            const wechatBindUrl = 'https://open.weixin.qq.com/connect/qrconnect?appid=wx1880d44d41318723&redirect_uri=https%3a%2f%2fsubfg.cn&response_type=code&scope=snsapi_login&state=BIND#wechat_redirect';
            window.location.href = wechatBindUrl;
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 pt-16">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {/* 页面标题 */}
                <div className="mb-8">
                    <Title heading={2} className="text-gray-900 dark:text-white mb-2">
                        {t('个人中心')}
                    </Title>
                    <Text className="text-gray-600 dark:text-gray-300">
                        {t('管理您的账户信息、安全设置和个人偏好')}
                    </Text>
                </div>

                {/* 响应式两栏布局 */}
                <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 lg:gap-8">
                    {/* 左侧用户信息卡片 */}
                    <div className="lg:col-span-4">
                        <div className="sticky top-24 lg:top-32">
                            <Card
                                className="profile-card shadow-xl border-0 bg-white/80  rounded-xl dark:bg-gray-800/80"
                            >
                                <div className="p-8">
                                    {/* 头像和基本信息 */}
                                    <div className="text-center mb-6">
                                        <div className="relative inline-block mb-4">

                                            <Avatar color="amber" border={{ color: 'blue' }}
                                                src={userState?.user?.avatarUrl} size={"extra-large"}
                                                style={{ marginRight: '8px' }}>T</Avatar>

                                        </div>

                                        <div className="space-y-2">
                                            <div className="flex items-center justify-center space-x-3">
                                                <Title heading={4} className="text-gray-900 dark:text-white mb-0">
                                                    {userState?.user?.userName || userState?.user?.email || t('未设置用户名')}
                                                </Title>
                                                {isRoot() && (
                                                    <Tag
                                                        color="red"
                                                        size="small"
                                                        className="animate-pulse shadow-sm"
                                                    >
                                                        <Shield size={12} className="mr-1" />
                                                        {t('超级管理员')}
                                                    </Tag>
                                                )}
                                                {isAdmin() && !isRoot() && (
                                                    <Tag
                                                        color="orange"
                                                        size="small"
                                                        className="shadow-sm"
                                                    >
                                                        <ShieldCheck size={12} className="mr-1" />
                                                        {t('管理员')}
                                                    </Tag>
                                                )}
                                            </div>

                                            {userState?.user?.motto && (
                                                <Text className="text-gray-600 dark:text-gray-300 italic">
                                                    "{userState.user.motto}"
                                                </Text>
                                            )}
                                        </div>
                                    </div>

                                    {/* 用户详细信息 */}
                                    <div className="space-y-4 mb-6">
                                        <div className="flex items-center justify-between  rounded-lg
                                                      bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100
                                                      dark:hover:bg-gray-700 transition-colors">
                                            <div className="flex items-center space-x-3">
                                                <IconMail className="text-blue-600 dark:text-blue-400" />
                                                <div>
                                                    <Text className="text-sm text-gray-500 dark:text-gray-400">
                                                        {t('邮箱地址')}&nbsp;
                                                    </Text>
                                                    <Text className="font-medium text-gray-900 dark:text-white">
                                                        {userState?.user?.email || t('未绑定邮箱')}
                                                    </Text>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="flex items-center justify-between  rounded-lg
                                                      bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100
                                                      dark:hover:bg-gray-700 transition-colors">
                                            <div className="flex items-center space-x-3">
                                                <IconCreditCard className="text-purple-600 dark:text-purple-400" />
                                                <div>
                                                    <Text className="text-sm text-gray-500 dark:text-gray-400">
                                                        {t('积分余额')}&nbsp;
                                                    </Text>
                                                    <Text className="font-medium text-gray-900 dark:text-white">
                                                        {userState?.user?.creditScore || 0}
                                                    </Text>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* 编辑按钮 */}
                                    <Button
                                        theme="solid"
                                        type="primary"
                                        icon={<IconEdit />}
                                        onClick={showEditProfileModal}
                                        className="profile-button w-full h-12 rounded-xl font-medium shadow-lg
                                                 hover:shadow-xl transform hover:scale-105 transition-all duration-200
                                                 bg-gradient-to-r from-blue-500 to-purple-600
                                                 hover:from-blue-600 hover:to-purple-700"
                                    >
                                        {t('编辑资料')}
                                    </Button>
                                </div>
                            </Card>
                        </div>
                    </div>

                    {/* 右侧功能选项卡 */}
                    <div className="lg:col-span-8">
                        <Card className="shadow-xl border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
                            <Tabs
                                type="line"
                                className="h-full"
                                tabBarStyle={{
                                    padding: '0 32px',
                                    borderBottom: '1px solid rgb(229 231 235)',
                                    background: 'transparent'
                                }}
                            >
                                <TabPane
                                    tab={
                                        <div className="flex items-center space-x-2 px-4 py-2">
                                            <Shield size={16} />
                                            <span>{t('账户安全')}</span>
                                        </div>
                                    }
                                    itemKey="security"
                                >
                                    <div className="tab-content p-1 space-y-6">
                                        {/* 密码修改 */}
                                        <div className="group">
                                            <Card className="border-0 bg-gradient-to-r from-blue-50 to-indigo-50
                                                           dark:from-blue-900/20 dark:to-indigo-900/20
                                                           hover:shadow-lg transition-all duration-300
                                                           hover:scale-[1.02] cursor-pointer">
                                                <div className="flex items-center justify-between ">
                                                    <div className="flex items-center space-x-4">
                                                        <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-xl
                                                                      group-hover:bg-blue-200 dark:group-hover:bg-blue-800/50
                                                                      transition-colors">
                                                            <IconLock size="large" className="text-blue-600 dark:text-blue-400" />
                                                        </div>
                                                        <div>
                                                            <Title heading={5} className="mb-1 text-gray-900 dark:text-white">
                                                                {t('登录密码')}
                                                            </Title>
                                                            <Text className="text-gray-600 dark:text-gray-300">
                                                                {t('定期更换密码，保护账户安全')}
                                                            </Text>
                                                        </div>
                                                    </div>
                                                    <Button
                                                        theme="solid"
                                                        type="primary"
                                                        onClick={showChangePasswordModal}
                                                        className="px-6 py-2 rounded-lg shadow-md hover:shadow-lg
                                                                 transform hover:scale-105 transition-all duration-200"
                                                    >
                                                        {t('修改密码')}
                                                    </Button>
                                                </div>
                                            </Card>
                                        </div>

                                        {/* 邮箱换绑 */}
                                        <div className="group">
                                            <Card className="border-0 bg-gradient-to-r from-green-50 to-emerald-50
                                                           dark:from-green-900/20 dark:to-emerald-900/20
                                                           hover:shadow-lg transition-all duration-300
                                                           hover:scale-[1.02] cursor-pointer">
                                                <div className="flex items-center justify-between ">
                                                    <div className="flex items-center space-x-4">
                                                        <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-xl
                                                                      group-hover:bg-green-200 dark:group-hover:bg-green-800/50
                                                                      transition-colors">
                                                            <IconMail size="large" className="text-green-600 dark:text-green-400" />
                                                        </div>
                                                        <div>
                                                            <Title heading={5} className="mb-1 text-gray-900 dark:text-white">
                                                                {t('邮箱地址')}
                                                            </Title>
                                                            <Text className="text-gray-600 dark:text-gray-300">
                                                                {userState?.user?.email || t('未绑定邮箱')}
                                                            </Text>
                                                        </div>
                                                    </div>
                                                    <Button
                                                        theme="solid"
                                                        type="primary"
                                                        onClick={showChangeEmailModal}
                                                        className="px-6 py-2 rounded-lg shadow-md hover:shadow-lg
                                                                 transform hover:scale-105 transition-all duration-200"
                                                    >
                                                        {t('换绑邮箱')}
                                                    </Button>
                                                </div>
                                            </Card>
                                        </div>

                                        {/* 微信绑定 */}
                                        <div className="group">
                                            <Card className="border-0 bg-gradient-to-r from-emerald-50 to-teal-50
                                                           dark:from-emerald-900/20 dark:to-teal-900/20
                                                           hover:shadow-lg transition-all duration-300
                                                           hover:scale-[1.02] cursor-pointer">
                                                <div className="flex items-center justify-between ">
                                                    <div className="flex items-center space-x-4">
                                                        <div className="p-3 bg-emerald-100 dark:bg-emerald-900/30 rounded-xl
                                                                      group-hover:bg-emerald-200 dark:group-hover:bg-emerald-800/50
                                                                      transition-colors">
                                                            <SiWechat size={24} className="text-emerald-600 dark:text-emerald-400" />
                                                        </div>
                                                        <div>
                                                            <Title heading={5} className="mb-1 text-gray-900 dark:text-white">
                                                                {t('微信账号')}
                                                            </Title>
                                                            <Text className="text-gray-600 dark:text-gray-300">
                                                                {wechatBinding ? t('已绑定微信账号') : t('未绑定微信账号')}
                                                            </Text>
                                                        </div>
                                                    </div>
                                                    <Button
                                                        theme={wechatBinding ? "outline" : "solid"}
                                                        type={wechatBinding ? "danger" : "primary"}
                                                        onClick={handleWechatBinding}
                                                        className="px-6 py-2 rounded-lg shadow-md hover:shadow-lg
                                                                 transform hover:scale-105 transition-all duration-200"
                                                    >
                                                        {wechatBinding ? t('解绑') : t('绑定')}
                                                    </Button>
                                                </div>
                                            </Card>
                                        </div>
                                    </div>
                                </TabPane>
                                <TabPane
                                    tab={
                                        <div className="flex items-center space-x-2 px-4 py-2">
                                            <Settings size={16} />
                                            <span>{t('使用统计')}</span>
                                        </div>
                                    }
                                    itemKey="stats"
                                >
                                    <div className="tab-content p-8">
                                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6">
                                            <Card className="stats-card text-center p-6 border-0 bg-gradient-to-br from-blue-50 to-blue-100
                                                           dark:from-blue-900/20 dark:to-blue-800/20">
                                                <div className="stats-icon p-4 bg-blue-100 dark:bg-blue-900/30 rounded-full w-16 h-16
                                                              mx-auto mb-4 flex items-center justify-center">
                                                    <UserPlus size={32} className="text-blue-600 dark:text-blue-400" />
                                                </div>
                                                <Title heading={3} className="mb-2 text-gray-900 dark:text-white">
                                                    {userState?.user?.customSubMCount || 0}
                                                </Title>
                                                <Text className="text-gray-600 dark:text-gray-300 font-medium">
                                                    {t('自定义订阅')}
                                                </Text>
                                            </Card>

                                            <Card className="stats-card text-center p-6 border-0 bg-gradient-to-br from-green-50 to-green-100
                                                           dark:from-green-900/20 dark:to-green-800/20">
                                                <div className="stats-icon p-4 bg-green-100 dark:bg-green-900/30 rounded-full w-16 h-16
                                                              mx-auto mb-4 flex items-center justify-center">
                                                    <Settings size={32} className="text-green-600 dark:text-green-400" />
                                                </div>
                                                <Title heading={3} className="mb-2 text-gray-900 dark:text-white">
                                                    {userState?.user?.createFgCount || 0}
                                                </Title>
                                                <Text className="text-gray-600 dark:text-gray-300 font-medium">
                                                    {t('创建拼车')}
                                                </Text>
                                            </Card>

                                            <Card className="stats-card text-center p-6 border-0 bg-gradient-to-br from-purple-50 to-purple-100
                                                           dark:from-purple-900/20 dark:to-purple-800/20">
                                                <div className="stats-icon p-4 bg-purple-100 dark:bg-purple-900/30 rounded-full w-16 h-16
                                                              mx-auto mb-4 flex items-center justify-center">
                                                    <CreditCard size={32} className="text-purple-600 dark:text-purple-400" />
                                                </div>
                                                <Title heading={3} className="mb-2 text-gray-900 dark:text-white">
                                                    {userState?.user?.joinFgCount || 0}
                                                </Title>
                                                <Text className="text-gray-600 dark:text-gray-300 font-medium">
                                                    {t('参与拼车')}
                                                </Text>
                                            </Card>
                                        </div>
                                    </div>
                                </TabPane>
                            </Tabs>
                        </Card>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default My;

