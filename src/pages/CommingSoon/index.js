import React from 'react';
import { Empty } from '@douyinfe/semi-ui';
import { IllustrationNotFound, IllustrationNotFoundDark } from '@douyinfe/semi-illustrations';
import { useTranslation } from 'react-i18next';

const CommingSoon = () => {
    const { t } = useTranslation();
    return (
        <div className="flex justify-center items-center h-screen p-8 mt-[64px]">
            <Empty
                image={<IllustrationConstruction style={{ width: 150, height: 150 }} />}
                darkModeImage={<IllustrationConstructionDark style={{ width: 150, height: 150 }} />}
                title={t('功能建设中')}
                description={t('当前功能暂未开放，敬请期待。')}
            />
        </div>
    );
};

export default CommingSoon;
