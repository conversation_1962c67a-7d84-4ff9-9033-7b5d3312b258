

import React from 'react';
import { reducer, initialState } from './reducer';

export const UserContext = React.createContext({
  state: initialState, // 用户状态
  dispatch: () => null, // 用户状态更新函数
});

export const UserProvider = ({ children }) => {
  const [state, dispatch] = React.useReducer(reducer, initialState); // 用户状态

  return (
    <UserContext.Provider value={[state, dispatch]}>
      {children}
    </UserContext.Provider>
  );
};
