import React, { createContext, useContext, useReducer } from 'react';

// 认证Modal类型
export const AUTH_MODAL_TYPES = {
  NONE: 'none',
  LOGIN: 'login',
  REGISTER: 'register',
  RESET_PASSWORD: 'reset_password',
  AUTH_CODE: 'auth_code',
  SET_NEW_PASSWORD: 'set_new_password',
  CHANGE_PASSWORD: 'change_password',
  CHANGE_EMAIL: 'change_email',
  EDIT_PROFILE: 'edit_profile',
};

// 初始状态
const initialState = {
  isVisible: false,
  modalType: AUTH_MODAL_TYPES.NONE,
  modalData: null, // 用于传递数据，如验证码页面需要的邮箱信息
  returnUrl: null, // 登录成功后返回的URL
};

// Reducer
const authModalReducer = (state, action) => {
  switch (action.type) {
    case 'SHOW_MODAL':
      return {
        ...state,
        isVisible: true,
        modalType: action.payload.modalType,
        modalData: action.payload.modalData || null,
        returnUrl: action.payload.returnUrl || null,
      };
    case 'HIDE_MODAL':
      return {
        ...state,
        isVisible: false,
        modalType: AUTH_MODAL_TYPES.NONE,
        modalData: null,
        returnUrl: null,
      };
    case 'SWITCH_MODAL':
      return {
        ...state,
        modalType: action.payload.modalType,
        modalData: action.payload.modalData || state.modalData,
      };
    case 'UPDATE_MODAL_DATA':
      return {
        ...state,
        modalData: { ...state.modalData, ...action.payload },
      };
    default:
      return state;
  }
};

// Context
const AuthModalContext = createContext();

// Provider组件
export const AuthModalProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authModalReducer, initialState);

  // 显示登录Modal
  const showLoginModal = (returnUrl = null) => {
    dispatch({
      type: 'SHOW_MODAL',
      payload: {
        modalType: AUTH_MODAL_TYPES.LOGIN,
        returnUrl,
      },
    });
  };

  // 显示注册Modal
  const showRegisterModal = (returnUrl = null) => {
    dispatch({
      type: 'SHOW_MODAL',
      payload: {
        modalType: AUTH_MODAL_TYPES.REGISTER,
        returnUrl,
      },
    });
  };

  // 显示密码重置Modal
  const showResetPasswordModal = () => {
    dispatch({
      type: 'SHOW_MODAL',
      payload: {
        modalType: AUTH_MODAL_TYPES.RESET_PASSWORD,
      },
    });
  };

  // 显示验证码Modal
  const showAuthCodeModal = (modalData) => {
    dispatch({
      type: 'SHOW_MODAL',
      payload: {
        modalType: AUTH_MODAL_TYPES.AUTH_CODE,
        modalData,
      },
    });
  };

  // 显示设置新密码Modal
  const showSetNewPasswordModal = (modalData) => {
    dispatch({
      type: 'SHOW_MODAL',
      payload: {
        modalType: AUTH_MODAL_TYPES.SET_NEW_PASSWORD,
        modalData,
      },
    });
  };

  // 显示修改密码Modal
  const showChangePasswordModal = () => {
    dispatch({
      type: 'SHOW_MODAL',
      payload: {
        modalType: AUTH_MODAL_TYPES.CHANGE_PASSWORD,
      },
    });
  };

  // 显示换绑邮箱Modal
  const showChangeEmailModal = () => {
    dispatch({
      type: 'SHOW_MODAL',
      payload: {
        modalType: AUTH_MODAL_TYPES.CHANGE_EMAIL,
      },
    });
  };

  // 显示编辑个人信息Modal
  const showEditProfileModal = () => {
    dispatch({
      type: 'SHOW_MODAL',
      payload: {
        modalType: AUTH_MODAL_TYPES.EDIT_PROFILE,
      },
    });
  };

  // 隐藏Modal
  const hideModal = () => {
    dispatch({ type: 'HIDE_MODAL' });
  };

  // 切换Modal类型
  const switchModal = (modalType, modalData = null) => {
    dispatch({
      type: 'SWITCH_MODAL',
      payload: { modalType, modalData },
    });
  };

  // 更新Modal数据
  const updateModalData = (data) => {
    dispatch({
      type: 'UPDATE_MODAL_DATA',
      payload: data,
    });
  };

  const value = {
    ...state,
    showLoginModal,
    showRegisterModal,
    showResetPasswordModal,
    showAuthCodeModal,
    showSetNewPasswordModal,
    showChangePasswordModal,
    showChangeEmailModal,
    showEditProfileModal,
    hideModal,
    switchModal,
    updateModalData,
  };

  return (
    <AuthModalContext.Provider value={value}>
      {children}
    </AuthModalContext.Provider>
  );
};

// Hook
export const useAuthModal = () => {
  const context = useContext(AuthModalContext);
  if (!context) {
    throw new Error('useAuthModal must be used within an AuthModalProvider');
  }
  return context;
};
