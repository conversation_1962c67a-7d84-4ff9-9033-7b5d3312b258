import { useCallback, useRef } from 'react';
import { MESSAGE_ROLES } from '../constants/playground.constants';

/**
 * 自定义Hook，用于在消息和自定义请求体之间进行双向同步
 * 
 * @param {boolean} customRequestMode - 是否启用自定义请求模式
 * @param {string} customRequestBody - 自定义请求体内容（JSON字符串）
 * @param {Array} message - 消息列表
 * @param {Object} inputs - 输入配置对象
 * @param {Function} setCustomRequestBody - 设置自定义请求体的函数
 * @param {Function} setMessage - 设置消息列表的函数
 * @param {Function} debouncedSaveConfig - 防抖保存配置的函数
 * @returns {Object} 包含syncMessageToCustomBody和syncCustomBodyToMessage两个同步函数的对象
 */
export const useSyncMessageAndCustomBody = (
  customRequestMode,
  customRequestBody,
  message,
  inputs,
  setCustomRequestBody,
  setMessage,
  debouncedSaveConfig
) => {
  const isUpdatingFromMessage = useRef(false);
  const isUpdatingFromCustomBody = useRef(false);
  const lastMessageHash = useRef('');
  const lastCustomBodyHash = useRef('');

  // 获取消息列表的哈希值，用于比较变化
  const getMessageHash = useCallback((messages) => {
    return JSON.stringify(messages.map(msg => ({
      id: msg.id,
      role: msg.role,
      content: msg.content
    })));
  }, []);

  // 从自定义请求体中提取消息并生成哈希值
  const getCustomBodyHash = useCallback((customBody) => {
    try {
      const parsed = JSON.parse(customBody);
      return JSON.stringify(parsed.messages || []);
    } catch {
      return '';
    }
  }, []);

  // 将消息同步到自定义请求体
  const syncMessageToCustomBody = useCallback(() => {
    if (!customRequestMode || isUpdatingFromCustomBody.current) return;

    const currentMessageHash = getMessageHash(message);
    if (currentMessageHash === lastMessageHash.current) return;

    try {
      isUpdatingFromMessage.current = true;
      let customPayload;

      // 尝试解析现有的自定义请求体，如果失败则创建默认结构
      try {
        customPayload = JSON.parse(customRequestBody || '{}');
      } catch {
        customPayload = {
          model: inputs.model || 'gpt-4o',
          messages: [],
          temperature: inputs.temperature || 0.7,
          stream: inputs.stream !== false
        };
      }

      // 更新消息部分
      customPayload.messages = message.map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      const newCustomBody = JSON.stringify(customPayload, null, 2);
      setCustomRequestBody(newCustomBody);
      lastMessageHash.current = currentMessageHash;
      lastCustomBodyHash.current = getCustomBodyHash(newCustomBody);

      setTimeout(() => {
        debouncedSaveConfig();
      }, 0);
    } finally {
      isUpdatingFromMessage.current = false;
    }
  }, [customRequestMode, customRequestBody, message, inputs.model, inputs.temperature, inputs.stream, getMessageHash, getCustomBodyHash, setCustomRequestBody, debouncedSaveConfig]);

  // 将自定义请求体同步到消息
  const syncCustomBodyToMessage = useCallback(() => {
    if (!customRequestMode || isUpdatingFromMessage.current) return;

    const currentCustomBodyHash = getCustomBodyHash(customRequestBody);
    if (currentCustomBodyHash === lastCustomBodyHash.current) return;

    try {
      isUpdatingFromCustomBody.current = true;
      const customPayload = JSON.parse(customRequestBody || '{}');

      if (customPayload.messages && Array.isArray(customPayload.messages)) {
        // 将自定义请求体中的消息转换为内部消息格式
        const newMessages = customPayload.messages.map((msg, index) => ({
          id: msg.id || (index + 1).toString(),
          role: msg.role || MESSAGE_ROLES.USER,
          content: msg.content || '',
          createAt: Date.now(),
          ...(msg.role === MESSAGE_ROLES.ASSISTANT && {
            reasoningContent: msg.reasoningContent || '',
            isReasoningExpanded: false
          })
        }));

        setMessage(newMessages);
        lastCustomBodyHash.current = currentCustomBodyHash;
        lastMessageHash.current = getMessageHash(newMessages);
      }
    } catch (error) {
      console.warn('同步自定义请求体到消息失败:', error);
    } finally {
      isUpdatingFromCustomBody.current = false;
    }
  }, [customRequestMode, customRequestBody, getCustomBodyHash, getMessageHash, setMessage]);

  return {
    syncMessageToCustomBody,
    syncCustomBodyToMessage
  };
};