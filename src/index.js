import React from 'react';
import ReactDOM from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';
import '@douyinfe/semi-ui/dist/css/semi.css';
import { UserProvider } from './context/User';
import 'react-toastify/dist/ReactToastify.css';
import { StatusProvider } from './context/Status';
import { ThemeProvider } from './context/Theme';
import { AuthModalProvider } from './context/Auth';
import PageLayout from './components/layout/PageLayout.js';
import AuthModalManager from './components/auth/AuthModalManager.js';
import './i18n/i18n.js';
import './index.css';




const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <StatusProvider>
    <UserProvider>
      <AuthModalProvider>
        <BrowserRouter
          future={{
            v7_startTransition: true,
            v7_relativeSplatPath: true,
          }}
        >
          <ThemeProvider>
            <PageLayout />
            <AuthModalManager />
          </ThemeProvider>
        </BrowserRouter>
      </AuthModalProvider>
    </UserProvider>
  </StatusProvider>
);
