import React, { lazy, Suspense } from 'react';
import { Route, Routes, useLocation } from 'react-router-dom';
import Loading from './components/common/Loading.js';
import { PrivateRoute } from './helpers';
import NotFound from './pages/NotFound';
import TopUp from './pages/TopUp';
import Market from './pages/Market';
import Management from './pages/Management';
import My from './pages/My';
import About from './pages/About';
import CommingSoon from './pages/CommingSoon';
const Home = lazy(() => import('./pages/Home'));
function App() {
  const location = useLocation();

  return (
    <Routes>
      <Route
        path='/'
        element={
          <Suspense fallback={<Loading></Loading>} key={location.pathname}>
            <Home />
          </Suspense>
        }
      />
      <Route
        path='/market'
        element={
          <Suspense fallback={<Loading></Loading>} key={location.pathname}>
            <Market />
          </Suspense>
        }
      />

      <Route
        path='/management'
        element={
          <PrivateRoute>
            <Suspense fallback={<Loading></Loading>} key={location.pathname}>
              <Management />
            </Suspense>
          </PrivateRoute>
        }
      />

      <Route
        path='/CommingSoon'
        element={<CommingSoon />}
      />
      <Route
        path='/my'
        element={
          <PrivateRoute>
            <Suspense fallback={<Loading></Loading>} key={location.pathname}>
              <My />
            </Suspense>
          </PrivateRoute>
        }
      />

      <Route
        path='/console/topup'
        element={
          <PrivateRoute>
            <Suspense fallback={<Loading></Loading>} key={location.pathname}>
              <TopUp />
            </Suspense>
          </PrivateRoute>
        }
      />


      <Route
        path='/about'
        element={
          <Suspense fallback={<Loading></Loading>} key={location.pathname}>
            <About />
          </Suspense>
        }
      />


      <Route path='*' element={<NotFound />} />
    </Routes>
  );
}

export default App;
