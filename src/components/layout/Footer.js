import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography } from '@douyinfe/semi-ui';
import { getLogo, getSystemName } from '../../helpers';
import FaceBookIcon from '../common/logo/FaceBookIcon.js';
import RedBookIcon from '../common/logo/RedBookIcon.js';
import TelegramIcon from '../common/logo/TelegramIcon.js';
import XIcon from '../common/logo/XIcon.js';
import YoutubeIcon from '../common/logo/YoutubeIcon.js';

const FooterBar = () => {
  const { t } = useTranslation();
  const systemName = getSystemName();
  const logo = getLogo();
  const isDemoSiteMode = true



  const currentYear = new Date().getFullYear();

  const customFooter = useMemo(() => (
    <footer className="relative h-auto py-16 px-6 md:px-24 w-full flex flex-col items-center justify-between overflow-hidden">
      <div className="absolute hidden md:block top-[204px] left-[-100px] w-[151px] h-[151px] rounded-full bg-[#FFD166]"></div>
      <div className="absolute md:hidden bottom-[20px] left-[-50px] w-[80px] h-[80px] rounded-full bg-[#FFD166] opacity-60"></div>

      {isDemoSiteMode && (
        <div className="flex flex-col md:flex-row justify-between w-full max-w-[1400px] mb-10 gap-8">
          <div className="flex-shrink-0 md:max-w-xs">
            <div className="flex items-center gap-4 flex-col">
              <img
                src={logo}
                alt={systemName}
                className="w-16 h-16 rounded-full bg-gray-800 p-1.5 object-contain"
              />
              <span className="shine-text font-bold text-lg text-center">{t('独乐乐，不如众分享')}</span>
            </div>

            {/* 社交媒体图标 */}
            <div className="flex items-center justify-center gap-4 mt-6">
              <a
                href="https://www.facebook.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-semi-color-text-1 hover:text-semi-color-primary transition-colors duration-200 hover:scale-110 transform"
                aria-label="Facebook"
              >
                <div className="w-6 h-6">
                  <FaceBookIcon />
                </div>
              </a>

              <a
                href="https://www.xiaohongshu.com/user/profile/5c7a6fa3000000001200a2be"
                target="_blank"
                rel="noopener noreferrer"
                className="text-semi-color-text-1 hover:text-semi-color-primary transition-colors duration-200 hover:scale-110 transform"
                aria-label="小红书"
              >
                <div className="w-6 h-6">
                  <RedBookIcon />
                </div>
              </a>

              <a
                href="https://t.me"
                target="_blank"
                rel="noopener noreferrer"
                className="text-semi-color-text-1 hover:text-semi-color-primary transition-colors duration-200 hover:scale-110 transform"
                aria-label="Telegram"
              >
                <div className="w-6 h-6">
                  <TelegramIcon />
                </div>
              </a>

              <a
                href="https://x.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-semi-color-text-1 hover:text-semi-color-primary transition-colors duration-200 hover:scale-110 transform"
                aria-label="X (Twitter)"
              >
                <div className="w-6 h-6">
                  <XIcon />
                </div>
              </a>

              <a
                href="https://www.youtube.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-semi-color-text-1 hover:text-semi-color-primary transition-colors duration-200 hover:scale-110 transform"
                aria-label="YouTube"
              >
                <div className="w-6 h-6">
                  <YoutubeIcon />
                </div>
              </a>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 md:gap-8 flex-1 md:max-w-2xl">
            <div className="text-left">
              <p className="!text-semi-color-text-0 font-semibold mb-4">{t('产品')}</p>
              <div className="flex flex-col gap-3">
                <a href="/" rel="noopener noreferrer" className="!text-semi-color-text-1 hover:!text-semi-color-primary transition-colors duration-200 text-sm">{t('首页')}</a>
                <a href="/market" rel="noopener noreferrer" className="!text-semi-color-text-1 hover:!text-semi-color-primary transition-colors duration-200 text-sm">{t('订阅市场')}</a>
                <a href="/management" rel="noopener noreferrer" className="!text-semi-color-text-1 hover:!text-semi-color-primary transition-colors duration-200 text-sm">{t('订阅管理')}</a>
              </div>
            </div>

            <div className="text-left">
              <p className="!text-semi-color-text-0 font-semibold mb-4">{t('关于')}</p>
              <div className="flex flex-col gap-3">
                <a href="/about" target="_blank" rel="noopener noreferrer" className="!text-semi-color-text-1 hover:!text-semi-color-primary transition-colors duration-200 text-sm">{t('关于')}</a>
                <a href="/coming" target="_blank" rel="noopener noreferrer" className="!text-semi-color-text-1 hover:!text-semi-color-primary transition-colors duration-200 text-sm">{t('招聘')}</a>
                <a href="/coming" target="_blank" rel="noopener noreferrer" className="!text-semi-color-text-1 hover:!text-semi-color-primary transition-colors duration-200 text-sm">{t('博客')}</a>
                <a href="/coming" target="_blank" rel="noopener noreferrer" className="!text-semi-color-text-1 hover:!text-semi-color-primary transition-colors duration-200 text-sm">{t('客服服务')}</a>
              </div>
            </div>

            <div className="text-left">
              <p className="!text-semi-color-text-0 font-semibold mb-4">{t('支持')}</p>
              <div className="flex flex-col gap-3">
                <a href="/faq" target="_blank" rel="noopener noreferrer" className="!text-semi-color-text-1 hover:!text-semi-color-primary transition-colors duration-200 text-sm">{t('常见问题')}</a>
                <a href="/contact-us" target="_blank" rel="noopener noreferrer" className="!text-semi-color-text-1 hover:!text-semi-color-primary transition-colors duration-200 text-sm">{t('联系我们')}</a>
                <a href="/privacy-policy" target="_blank" rel="noopener noreferrer" className="!text-semi-color-text-1 hover:!text-semi-color-primary transition-colors duration-200 text-sm">{t('隐私政策')}</a>
                <a href="/terms-of-service" target="_blank" rel="noopener noreferrer" className="!text-semi-color-text-1 hover:!text-semi-color-primary transition-colors duration-200 text-sm">{t('服务条款')}</a>
              </div>
            </div>


          </div>
        </div>
      )}

      <div className="flex flex-col md:flex-row items-center justify-center w-full max-w-[1110px] gap-6 border-t border-semi-color-border pt-6">
        <div className="flex flex-wrap items-center gap-2 pt-2">
          <Typography.Text className="text-sm !text-semi-color-text-1">© {currentYear} {systemName}. {t('版权所有')}</Typography.Text>
        </div>

      </div>
    </footer >
  ), [logo, systemName, t, currentYear, isDemoSiteMode]);



  return (
    <div className="w-full">
      {customFooter}
    </div>
  );
};

export default FooterBar;
