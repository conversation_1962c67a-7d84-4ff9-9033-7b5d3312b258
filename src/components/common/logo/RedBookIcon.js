

import React from 'react';
import { Icon } from '@douyinfe/semi-ui';

const RedBookIcon = () => {
    function CustomIcon() {
        return (
            <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g id="Group 1261156705">
                    <g id="Group 1261156704">
                        <g id="Group 73">
                            <rect id="Social Media Icon" width="36" height="36" rx="8" fill="#E0E9F4" />
                        </g>
                        <g id="&#229;&#176;&#143;&#231;&#186;&#162;&#228;&#185;&#166; 1" clipPath="url(#clip0_2829_74664)">
                            <path id="Vector" d="M7.23306 15.6308H5.41935C5.57661 17.9897 5 19.7825 5 19.7825L5.975 21.5542C7.25403 21.0091 7.23306 15.6308 7.23306 15.6308ZM13.104 15.6308H11.2903C11.2903 15.6308 11.2589 21.0091 12.5484 21.5542L13.5234 19.7825C13.5234 19.7825 12.9468 17.9897 13.104 15.6308ZM8.36532 21.0405H7.23306C7.23306 22.372 8.36532 22.4873 8.36532 22.4873H8.94194C10.0952 22.4873 10.1476 21.1663 10.1476 21.1663V13.272H8.36532V21.0405ZM14.4565 17.2558H15.6306L14.729 19.0905C14.729 19.0905 14.2048 20.1179 15.0435 20.1599H17.004L17.7903 18.7446H16.8153C16.6895 18.7446 16.6056 18.6083 16.6685 18.5034L18.1258 15.6413H16.4274L16.3226 15.851H16.2177C16.0919 15.851 16.0081 15.7147 16.071 15.6099L17.2871 13.2929H15.5887L14.1419 16.2075C14.1419 16.197 13.6177 17.2244 14.4565 17.2558ZM14.7081 20.7365C14.7081 20.7365 14.4879 20.7575 14.3516 20.6107L13.471 22.2986C13.471 22.2986 13.6177 22.4978 13.9113 22.4978H16.0919L17.025 20.7365H14.7081Z" fill="#596780" />
                            <path id="Vector_2" d="M21.2813 15.6306H22.4031V13.8379H18.3983V15.6306H19.541V20.7887H17.8636L17.0144 22.4871H22.9797V20.7887H21.2813V15.6306ZM29.3015 16.9621H29.0918V15.7984C29.0918 14.708 28.2112 13.8274 27.1209 13.8274H26.4394V13.2822H24.7096V13.8274H23.6192V15.5887H24.7096V16.9621H22.9797V18.7234H24.7096V22.4871H26.4394V18.7234H28.8192C29.0499 18.7234 29.2386 18.9121 29.2386 19.1427V20.9984H27.5926C27.5926 21.8161 28.2531 22.4871 29.0813 22.4871H29.5321C30.3499 22.4871 31.0209 21.8266 31.0209 20.9984V18.6605C31.0104 17.7274 30.245 16.9621 29.3015 16.9621ZM26.4394 15.5887H27.058C27.2047 15.5887 27.3305 15.704 27.3305 15.8613V16.9621H26.4394V15.5887Z" fill="#596780" />
                            <path id="Vector_3" d="M30.9791 14.6873C30.9791 14.2155 30.5912 13.8276 30.1194 13.8276C29.6477 13.8276 29.2598 14.2155 29.2598 14.6873V15.547H30.1194C30.6017 15.547 30.9791 15.1591 30.9791 14.6873Z" fill="#596780" />
                        </g>
                    </g>
                </g>
                <defs>
                    <clipPath id="clip0_2829_74664">
                        <rect width="26" height="26" fill="white" transform="translate(5 5)" />
                    </clipPath>
                </defs>
            </svg>

        );
    }

    return (
        <div>
            <Icon svg={<CustomIcon />} />
        </div>
    );
};

export default RedBookIcon;
