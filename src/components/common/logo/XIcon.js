

import React from 'react';
import { Icon } from '@douyinfe/semi-ui';

const XIcon = () => {
    function CustomIcon() {
        return (

            <svg width="37" height="36" viewBox="0 0 37 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g id="Social Media Icon Square/Twitter">
                    <rect id="Social Media Icon" x="0.198975" width="36" height="36" rx="8" fill="#E0E9F4" />
                    <path id="Twitter" d="M21.226 10.7015V10.6982H22.07L22.3784 10.7598C22.584 10.7998 22.7707 10.8522 22.9384 10.9171C23.1061 10.9819 23.2684 11.0575 23.4253 11.144C23.5822 11.2304 23.7245 11.3185 23.8522 11.4082C23.9788 11.4968 24.0924 11.5908 24.193 11.6902C24.2926 11.7907 24.4478 11.8166 24.6588 11.768C24.8698 11.7194 25.0971 11.6518 25.3405 11.5654C25.584 11.479 25.8248 11.3817 26.0628 11.2736C26.3009 11.1656 26.4459 11.097 26.4978 11.0678C26.5487 11.0375 26.5757 11.0213 26.579 11.0192L26.5822 11.0143L26.5984 11.0062L26.6146 10.9981L26.6309 10.99L26.6471 10.9819L26.6504 10.977L26.6552 10.9738L26.6601 10.9705L26.6633 10.9657L26.6796 10.9608L26.6958 10.9576L26.6926 10.9819L26.6877 11.0062L26.6796 11.0305L26.6715 11.0548L26.6633 11.071L26.6552 11.0872L26.6471 11.1116C26.6417 11.1278 26.6363 11.1494 26.6309 11.1764C26.6255 11.2034 26.5741 11.3115 26.4767 11.5006C26.3793 11.6897 26.2576 11.8815 26.1115 12.076C25.9654 12.2705 25.8345 12.4174 25.7187 12.5168C25.6019 12.6173 25.5245 12.6875 25.4866 12.7275C25.4487 12.7686 25.4028 12.8064 25.3486 12.841L25.2675 12.8945L25.2513 12.9026L25.235 12.9107L25.2318 12.9156L25.2269 12.9188L25.222 12.922L25.2188 12.9269L25.2026 12.935L25.1863 12.9431L25.1831 12.948L25.1782 12.9512L25.1734 12.9545L25.1701 12.9593L25.1669 12.9642L25.162 12.9674L25.1571 12.9707L25.1539 12.9755H25.235L25.6895 12.8783C25.9925 12.8134 26.2819 12.7351 26.5578 12.6433L26.9961 12.4974L27.0448 12.4812L27.0691 12.4731L27.0853 12.465L27.1016 12.4569L27.1178 12.4488L27.134 12.4406L27.1665 12.4358L27.199 12.4325V12.465L27.1908 12.4682L27.1827 12.4731L27.1795 12.4779L27.1746 12.4812L27.1697 12.4844L27.1665 12.4893L27.1633 12.4941L27.1584 12.4974L27.1535 12.5006L27.1503 12.5055L27.147 12.5103L27.1422 12.5136L27.134 12.5298L27.1259 12.546L27.121 12.5492C27.1189 12.5525 27.0502 12.6443 26.9149 12.8248C26.7797 13.0063 26.7066 13.0982 26.6958 13.1003C26.685 13.1036 26.6698 13.1198 26.6504 13.149C26.632 13.1792 26.5173 13.2997 26.3063 13.5104C26.0953 13.7211 25.8886 13.9086 25.6863 14.0728C25.4828 14.2382 25.38 14.4413 25.3779 14.6823C25.3746 14.9222 25.3622 15.1934 25.3405 15.4959C25.3189 15.7985 25.2783 16.1254 25.2188 16.4765C25.1593 16.8277 25.0673 17.2248 24.9429 17.6679C24.8184 18.1109 24.6669 18.5431 24.4884 18.9645C24.3099 19.3859 24.1232 19.7641 23.9284 20.0991C23.7337 20.4341 23.5551 20.7177 23.3928 20.9501C23.2305 21.1824 23.0655 21.4012 22.8978 21.6065C22.7301 21.8118 22.518 22.0431 22.2616 22.3002C22.004 22.5563 21.8634 22.6968 21.8396 22.7216C21.8147 22.7454 21.7086 22.834 21.5214 22.9875C21.3353 23.142 21.1351 23.2965 20.9209 23.451C20.7077 23.6045 20.5119 23.7325 20.3333 23.8352C20.1548 23.9378 19.9395 24.055 19.6873 24.1869C19.4363 24.3198 19.1647 24.443 18.8725 24.5564C18.5804 24.6699 18.272 24.7752 17.9474 24.8725C17.6228 24.9697 17.309 25.0454 17.006 25.0994C16.703 25.1534 16.3595 25.1994 15.9753 25.2372L15.3991 25.2939V25.302H14.3441V25.2939L14.2062 25.2858C14.1142 25.2804 14.0385 25.275 13.9789 25.2696C13.9194 25.2642 13.6949 25.2345 13.3054 25.1805C12.9158 25.1264 12.6101 25.0724 12.3883 25.0184C12.1665 24.9643 11.8365 24.8617 11.3982 24.7104C10.96 24.5591 10.5851 24.4062 10.2734 24.2517C9.9629 24.0983 9.76813 24.001 9.68913 23.96C9.61122 23.92 9.52358 23.8703 9.42619 23.8108L9.28011 23.7217L9.27688 23.7168L9.272 23.7136L9.26713 23.7104L9.26388 23.7055L9.24765 23.6974L9.23142 23.6893L9.22819 23.6844L9.22331 23.6812L9.21844 23.6779L9.21519 23.6731L9.21196 23.6682L9.20707 23.665H9.19896V23.6326L9.21519 23.6358L9.23142 23.6407L9.30446 23.6488C9.35315 23.6542 9.48571 23.6623 9.70212 23.6731C9.91854 23.6839 10.1485 23.6839 10.3919 23.6731C10.6354 23.6623 10.8843 23.6379 11.1385 23.6001C11.3928 23.5623 11.6931 23.4975 12.0394 23.4056C12.3856 23.3138 12.7037 23.2046 12.9937 23.0782C13.2827 22.9507 13.4882 22.8556 13.6105 22.793C13.7317 22.7314 13.9167 22.6168 14.1656 22.4493L14.5389 22.1981L14.5422 22.1932L14.547 22.19L14.5519 22.1868L14.5551 22.1819L14.5584 22.177L14.5633 22.1738L14.5681 22.1706L14.5714 22.1657L14.5876 22.1608L14.6038 22.1576L14.6071 22.1414L14.6119 22.1252L14.6168 22.1219L14.6201 22.1171L14.4902 22.109C14.4037 22.1036 14.3198 22.0982 14.2386 22.0928C14.1575 22.0874 14.0303 22.063 13.8572 22.0198C13.6841 21.9766 13.4974 21.9118 13.2972 21.8253C13.0971 21.7389 12.9023 21.6362 12.7129 21.5174C12.5236 21.3985 12.3867 21.2996 12.3023 21.2207C12.219 21.1429 12.1108 21.0327 11.9777 20.8901C11.8457 20.7464 11.731 20.5989 11.6336 20.4476C11.5362 20.2963 11.4431 20.1218 11.3544 19.9241L11.2197 19.6291L11.2116 19.6048L11.2035 19.5805L11.1986 19.5642L11.1954 19.548L11.2197 19.5513L11.244 19.5561L11.4226 19.5805C11.5416 19.5967 11.7283 19.6021 11.9825 19.5967C12.2368 19.5913 12.4127 19.5805 12.5101 19.5642C12.6074 19.548 12.667 19.5372 12.6886 19.5318L12.7211 19.5237L12.7616 19.5156L12.8022 19.5075L12.8055 19.5027L12.8103 19.4994L12.8152 19.4962L12.8184 19.4913L12.786 19.4832L12.7535 19.4751L12.7211 19.467L12.6886 19.4589L12.6561 19.4508C12.6345 19.4454 12.5966 19.4346 12.5425 19.4184C12.4884 19.4022 12.3423 19.3427 12.1043 19.2401C11.8662 19.1374 11.6769 19.0375 11.5362 18.9402C11.3952 18.8427 11.2607 18.736 11.1337 18.6209C11.0071 18.5042 10.868 18.354 10.7165 18.1703C10.5651 17.9866 10.4298 17.7732 10.3108 17.5301C10.1918 17.287 10.1025 17.0546 10.043 16.8331C9.98368 16.6129 9.94457 16.3878 9.92612 16.1605L9.89689 15.8201L9.91312 15.8233L9.92935 15.8282L9.94558 15.8363L9.96181 15.8444L9.97804 15.8525L9.99427 15.8606L10.2458 15.9741C10.4136 16.0497 10.6219 16.1146 10.8707 16.1686C11.1196 16.2226 11.2684 16.2523 11.3171 16.2577L11.3901 16.2658H11.5362L11.533 16.261L11.5281 16.2577L11.5232 16.2545L11.52 16.2496L11.5167 16.2448L11.5119 16.2415L11.507 16.2383L11.5037 16.2334L11.4875 16.2253L11.4713 16.2172L11.468 16.2123L11.4632 16.2091L11.4583 16.2059L11.455 16.201L11.4388 16.1929L11.4226 16.1848L11.4194 16.1799C11.4161 16.1778 11.3696 16.1432 11.2798 16.0762C11.191 16.0081 11.098 15.9201 11.0006 15.812C10.9032 15.7039 10.8058 15.5905 10.7084 15.4716C10.6109 15.3525 10.524 15.225 10.4487 15.0907C10.373 14.9557 10.2929 14.7838 10.2085 14.5753C10.1252 14.3678 10.0619 14.1587 10.0186 13.948C9.97535 13.7373 9.951 13.5293 9.94558 13.324C9.94017 13.1187 9.94558 12.9431 9.96181 12.7972C9.97804 12.6514 10.0105 12.4866 10.0592 12.3029C10.1079 12.1192 10.1782 11.9247 10.2702 11.7194L10.4082 11.4114L10.4163 11.3871L10.4244 11.3628L10.4293 11.3595L10.4325 11.3547L10.4357 11.3498L10.4406 11.3466L10.4455 11.3498L10.4487 11.3547L10.452 11.3595L10.4568 11.3628L10.4617 11.366L10.465 11.3709L10.4682 11.3758L10.4731 11.379L10.4812 11.3952L10.4893 11.4114L10.4942 11.4147L10.4974 11.4195L10.7165 11.6626C10.8626 11.8247 11.0358 12.0057 11.2359 12.2056C11.4361 12.4055 11.547 12.5093 11.5687 12.5168C11.5903 12.5255 11.6174 12.5503 11.6498 12.5914C11.6823 12.6314 11.7905 12.727 11.9744 12.8783C12.1584 13.0295 12.3991 13.2051 12.6967 13.405C12.9943 13.6049 13.3243 13.8022 13.6868 13.9967C14.0493 14.1912 14.4388 14.3667 14.8554 14.5234C15.272 14.6801 15.5642 14.7828 15.7319 14.8314C15.8996 14.88 16.1863 14.9421 16.5921 15.0178C16.9979 15.0934 17.3036 15.1421 17.5091 15.1637C17.7147 15.1853 17.8554 15.1977 17.9312 15.2009L18.0448 15.2042L18.0415 15.1799L18.0367 15.1556L18.0042 14.9529C17.9826 14.8179 17.9717 14.6288 17.9717 14.3857C17.9717 14.1425 17.9907 13.9183 18.0285 13.713C18.0664 13.5077 18.1232 13.2997 18.199 13.089C18.2747 12.8783 18.3488 12.7092 18.4213 12.5817C18.4949 12.4552 18.5912 12.311 18.7102 12.1489C18.8293 11.9868 18.9835 11.8193 19.1728 11.6464C19.3622 11.4735 19.5786 11.3196 19.822 11.1845C20.0655 11.0494 20.29 10.9468 20.4956 10.8765C20.7012 10.8063 20.8744 10.7604 21.015 10.7388C21.1557 10.7172 21.226 10.7047 21.226 10.7015Z" fill="#596780" />
                </g>
            </svg>

        );
    }

    return (
        <div>
            <Icon svg={<CustomIcon />} />
        </div>
    );
};

export default XIcon;
