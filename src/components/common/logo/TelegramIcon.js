

import React from 'react';
import { Icon } from '@douyinfe/semi-ui';

const TelegramIcon = () => {
    function CustomIcon() {
        return (

            <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g id="Group 1261156703">
                    <g id="Group 1261156702">
                        <rect id="Social Media Icon" width="36" height="36" rx="8" fill="#E0E9F4" />
                        <g id="Group 1261156701">
                            <path id="Element" d="M17.801 27C22.7716 27 26.801 22.9706 26.801 18C26.801 13.0294 22.7716 9 17.801 9C12.8305 9 8.80103 13.0294 8.80103 18C8.80103 22.9706 12.8305 27 17.801 27Z" fill="#596780" />
                            <path id="Element_2" fillRule="evenodd" clipRule="evenodd" d="M12.875 17.905C15.4987 16.7619 17.2483 16.0083 18.1236 15.6442C20.623 14.6046 21.1424 14.424 21.4809 14.418C21.5553 14.4167 21.7218 14.4352 21.8296 14.5227C21.9207 14.5966 21.9457 14.6964 21.9577 14.7664C21.9697 14.8364 21.9846 14.996 21.9728 15.1207C21.8373 16.5438 21.2513 19.9973 20.9531 21.5912C20.827 22.2657 20.5785 22.4918 20.3381 22.514C19.8154 22.562 19.4185 22.1686 18.9123 21.8367C18.1202 21.3175 17.6727 20.9943 16.9038 20.4876C16.0152 19.902 16.5913 19.5802 17.0977 19.0542C17.2302 18.9166 19.533 16.822 19.5776 16.632C19.5831 16.6082 19.5883 16.5196 19.5357 16.4729C19.4831 16.4261 19.4054 16.4421 19.3493 16.4548C19.2699 16.4728 18.0045 17.3092 15.5531 18.964C15.1939 19.2106 14.8686 19.3308 14.5771 19.3245C14.2557 19.3176 13.6376 19.1428 13.1781 18.9934C12.6144 18.8102 12.1665 18.7134 12.2055 18.4022C12.2258 18.2401 12.449 18.0744 12.875 17.905Z" fill="#E0E9F4" />
                        </g>
                    </g>
                </g>
            </svg>

        );
    }

    return (
        <div>
            <Icon svg={<CustomIcon />} />
        </div>
    );
};

export default TelegramIcon;
