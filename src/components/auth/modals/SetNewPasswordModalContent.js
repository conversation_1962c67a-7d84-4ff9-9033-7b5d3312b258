import React, { useState } from 'react';
import { useAuthModal, AUTH_MODAL_TYPES } from '../../../context/Auth/index.js';
import {
  API,
  showError,
  showSuccess,
} from '../../../helpers/index.js';
import {
  Button,
  Form,
  Progress,
  PinCode,
} from '@douyinfe/semi-ui';
import Text from '@douyinfe/semi-ui/lib/es/typography/text';
import { IconLock, IconMail } from '@douyinfe/semi-icons';
import { useTranslation } from 'react-i18next';
import AuthModalLayout from '../AuthModalLayout.js';

const SetNewPasswordModalContent = () => {
  const { t } = useTranslation();
  const { hideModal, switchModal, modalData } = useAuthModal();

  // 从modalData获取传递的数据
  const {
    email = ''
  } = modalData || {};

  const [formData, setFormData] = useState({
    emailCode: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [loading, setLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [passwordStrength, setPasswordStrength] = useState(0);

  const { emailCode, newPassword, confirmPassword } = formData;

  // 倒计时逻辑
  React.useEffect(() => {
    let timer;
    if (countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  /**
   * 处理表单字段变化
   * @param {string} field - 字段名
   * @param {string} value - 字段值
   */
  const handleFieldChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    if (field === 'newPassword') {
      setPasswordStrength(calculatePasswordStrength(value));
    }
  };

  /**
   * 计算密码强度
   * @param {string} password - 密码
   * @returns {number} 强度分数 (0-100)
   */
  const calculatePasswordStrength = (password) => {
    let strength = 0;

    if (password.length >= 8) strength += 25;
    if (password.length >= 12) strength += 15;
    if (/[a-z]/.test(password)) strength += 15;
    if (/[A-Z]/.test(password)) strength += 15;
    if (/[0-9]/.test(password)) strength += 15;
    if (/[^A-Za-z0-9]/.test(password)) strength += 15;

    return Math.min(strength, 100);
  };

  /**
   * 获取密码强度文本和颜色
   * @param {number} strength - 强度分数
   * @returns {object} 包含文本和颜色的对象
   */
  const getPasswordStrengthInfo = (strength) => {
    if (strength < 30) {
      return { text: t('弱'), color: 'var(--semi-color-danger)' };
    } else if (strength < 60) {
      return { text: t('中等'), color: 'var(--semi-color-warning)' };
    } else if (strength < 80) {
      return { text: t('强'), color: 'var(--semi-color-success)' };
    } else {
      return { text: t('很强'), color: 'var(--semi-color-success)' };
    }
  };

  /**
   * 重发验证码
   */
  const handleResendCode = async () => {
    if (countdown > 0) return;

    setResendLoading(true);
    try {
      const res = await API.post('/api/v3/auth/sendEmailCode', {
        email,
        type: 3 // 3-忘记密码
      });

      const { success = res.data.code == 200, message = res.data.message } = res.data;
      if (success) {
        showSuccess(t('验证码发送成功！'));
        setCountdown(60); // 60秒倒计时
      } else {
        showError(t(message));
      }
    } catch (error) {
      showError(t(error.response.data.message));
    } finally {
      setResendLoading(false);
    }
  };

  const validateForm = () => {
    if (!emailCode) {
      showError(t('请输入验证码'));
      return false;
    }

    if (emailCode.length !== 6) {
      showError(t('请输入完整的验证码'));
      return false;
    }

    if (!newPassword) {
      showError(t('请输入新密码'));
      return false;
    }

    if (!newPassword.match(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/)) {
      showError(t('密码长度不得小于 8 位，必须包含大小写字母、数字和特殊字符'));
      return false;
    }

    if (!confirmPassword) {
      showError(t('请确认新密码'));
      return false;
    }

    if (newPassword !== confirmPassword) {
      showError(t('两次输入的密码不一致'));
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }
    setLoading(true);
    try {
      const res = await API.post('/api/v3/user/forgetPassword', {
        email,
        emailCode,
        newPassword,
        confirmPassword
      });

      const { success = res.data.code == 200, message = res.data.message } = res.data;
      if (success) {
        showSuccess(t('密码重置成功！'));
        // 切换到登录页面
        switchModal(AUTH_MODAL_TYPES.LOGIN);
      } else {
        showError(message || t('密码重置失败'));
      }
    } catch (error) {
      showError(t(error.response.data.message));
    } finally {
      setLoading(false);
    }
  };

  const strengthInfo = getPasswordStrengthInfo(passwordStrength);

  return (
    <AuthModalLayout
      visible={true}
      onClose={hideModal}
      title={t('设置新密码')}
      subtitle={
        <>
          {t('我们已向')} <span className="font-medium text-gray-700">{email}</span> {t('发送了验证码')}
        </>
      }
      gradientFrom="from-indigo-500"
      gradientTo="to-purple-600"
    >
      {/* 验证码和密码设置表单 */}
      <Form className="space-y-4">
        {/* 验证码输入 */}
        <div className="space-y-2">
          <Text className="text-sm font-medium text-gray-700">{t('邮箱验证码')}</Text>
          <div className="flex justify-center">
            <PinCode
              size="large"
              count={6}
              value={emailCode}
              onChange={(value) => handleFieldChange('emailCode', value)}
              onComplete={(value) => handleFieldChange('emailCode', value)}
              autoFocus
            />
          </div>


        </div>
        <Form.Input
          field="newPassword"
          label={t('新密码')}
          placeholder={t('请输入新密码（至少8位）')}
          name="newPassword"
          mode="password"
          size="large"
          value={newPassword}
          onChange={(value) => handleFieldChange('newPassword', value)}
          prefix={<IconLock />}
          autoComplete="new-password"
        />

        {/* 密码强度指示器 */}
        {newPassword && (
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <Text className="text-sm text-gray-500">{t('密码强度')}</Text>
              <Text className="text-sm" style={{ color: strengthInfo.color }}>
                {strengthInfo.text}
              </Text>
            </div>
            <Progress
              percent={passwordStrength}
              showInfo={false}
              stroke={strengthInfo.color}
              size="small"
            />
          </div>
        )}

        <Form.Input
          field="confirmPassword"
          label={t('确认新密码')}
          placeholder={t('请再次输入新密码')}
          name="confirmPassword"
          mode="password"
          size="large"
          value={confirmPassword}
          onChange={(value) => handleFieldChange('confirmPassword', value)}
          prefix={<IconLock />}
          autoComplete="new-password"
        />

        <Button
          theme="solid"
          className="w-full !rounded-full"
          type="primary"
          size="large"
          onClick={handleSubmit}
          loading={loading}
          disabled={!emailCode || !newPassword || !confirmPassword || newPassword !== confirmPassword || emailCode.length !== 6}
        >
          {t('重置密码')}
        </Button>
      </Form>

      {/* 返回链接 */}
      <div className="text-center text-sm">
        <Text>
          <Button
            theme="borderless"
            type="tertiary"
            size="small"
            onClick={() => switchModal(AUTH_MODAL_TYPES.LOGIN)}
          >
            {t('返回登录')}
          </Button>
        </Text>
      </div>
    </AuthModalLayout>
  );
};

export default SetNewPasswordModalContent;
