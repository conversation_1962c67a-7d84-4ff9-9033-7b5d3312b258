import React, { useContext, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { UserContext } from '../../../context/User/index.js';
import { useAuthModal, AUTH_MODAL_TYPES } from '../../../context/Auth/index.js';
import {
  API,
  showError,
  showInfo,
  showSuccess,
  setUserData,
  setToken,
} from '../../../helpers/index.js';

import {
  Button,
  Form,
  Divider,
  Icon,
} from '@douyinfe/semi-ui';
import Text from '@douyinfe/semi-ui/lib/es/typography/text';
import { IconMail, IconLock } from '@douyinfe/semi-icons';
import { useTranslation } from 'react-i18next';
import AuthModalLayout from '../AuthModalLayout.js';
import WeChatIcon from '../../common/logo/WeChatIcon.js';


const LoginModalContent = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [userState, userDispatch] = useContext(UserContext);
  const { hideModal, switchModal, returnUrl } = useAuthModal();

  const [inputs, setInputs] = useState({
    username: '',
    password: '',
  });
  const [wechatLoading, setWechatLoading] = useState(false);
  const [loginLoading, setLoginLoading] = useState(false);
  const [resetPasswordLoading, setResetPasswordLoading] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const { username, password } = inputs;
  const status = JSON.parse(localStorage.getItem('status') || '{}');

  useEffect(() => {
    if (status.turnstile_check) {
      setTurnstileEnabled(true);
      setTurnstileSiteKey(status.turnstile_site_key);
    }
  }, [status]);

  const handleChange = (name, value) => {
    setInputs(inputs => ({ ...inputs, [name]: value }));
  };

  // 邮箱登录提交
  const handleSubmit = async () => {
    setSubmitted(true);
    setLoginLoading(true);
    try {
      if (username && password) {
        const res = await API.post('/api/v3/auth/login', {
          email: username,
          password,
        });
        const { success = res.data.code == 200, data } = res.data;
        if (success) {
          userDispatch({ type: 'login', payload: data.user });
          setUserData(data.user);
          console.log(data.token);

          setToken(data.token);
          showSuccess(t('登录成功！'));
          hideModal();
          if (returnUrl) {
            navigate(returnUrl);
          }
        } else {
          showError(data.message || t('登录失败'));
        }
      } else {
        showError(t('请输入用户名和密码！'));
      }
    } catch (error) {
      showError(t('密码错误或邮箱未验证'));
    } finally {
      setLoginLoading(false);
    }
  };

  // 微信登录
  const onWeChatLoginClicked = () => {
    setWechatLoading(true);
    const wechatLoginUrl = 'https://open.weixin.qq.com/connect/qrconnect?appid=wx1880d44d41318723&redirect_uri=https%3a%2f%2fsubfg.cn&response_type=code&scope=snsapi_login&state=STATE#wechat_redirect';
    window.location.href = wechatLoginUrl;
  };

  // 密码重置
  const handleResetPasswordClick = () => {
    switchModal(AUTH_MODAL_TYPES.RESET_PASSWORD);
  };

  return (
    <AuthModalLayout
      visible={true}
      onClose={hideModal}
      title={t('登 录')}
      subtitle={t('欢迎回来，请登录您的账户')}
      gradientFrom="from-blue-500"
      gradientTo="to-purple-600"
    >
      {/* 邮箱登录表单 */}
      <Form className="space-y-4">
        <Form.Input
          field="username"
          label={t('邮箱')}
          placeholder={t('请输入您的邮箱地址')}
          name="username"
          size="large"
          onChange={(value) => handleChange('username', value)}
          prefix={<IconMail />}
          autoComplete="new-password"
        />

        <Form.Input
          field="password"
          label={t('密码')}
          placeholder={t('请输入您的密码')}
          name="password"
          mode="password"
          size="large"
          onChange={(value) => handleChange('password', value)}
          prefix={<IconLock />}
          autoComplete="new-password"
        />

        <div className="flex flex-row gap-2">
          <Button
            theme="solid"
            className="flex-1 !rounded-full"
            type="primary"
            htmlType="submit"
            size="large"
            onClick={handleSubmit}
            loading={loginLoading}
          >
            {t('继续')}
          </Button>

          <Button
            theme='light'
            type='primary'
            className="flex-1 !rounded-full"
            size="large"
            onClick={handleResetPasswordClick}
            loading={resetPasswordLoading}
          >
            {t('忘记密码')}
          </Button>
        </div>
      </Form>

      {/* OAuth 登录选项 */}
      {(
        <>
          <Divider margin='12px' align='center'>
            {t('或')}
          </Divider>

          <div className="space-y-3">
            {/* 微信登录 */}
            {(
              <Button
                theme='outline'
                className="w-full h-12 flex items-center justify-center !rounded-full border border-gray-200 hover:bg-gray-50 transition-colors"
                type="tertiary"
                icon={<Icon svg={<WeChatIcon />} style={{ color: '#07C160' }} />}
                size="large"
                onClick={onWeChatLoginClicked}
                loading={wechatLoading}
              >
                <span className="ml-3">{t('使用 微信 继续')}</span>
              </Button>
            )}
          </div>
        </>
      )}

      {/* 注册链接 */}
      <div className="text-center text-sm">
        <Text>
          {t('没有账户？')}{' '}
          <Button
            theme="borderless"
            type="primary"
            size="small"
            onClick={() => switchModal(AUTH_MODAL_TYPES.REGISTER)}
          >
            {t('注册')}
          </Button>
        </Text>
      </div>

    </AuthModalLayout>
  );
};

export default LoginModalContent;
