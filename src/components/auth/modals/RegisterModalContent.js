import React, { useContext, useEffect, useState } from 'react';
import { UserContext } from '../../../context/User/index.js';
import { useAuthModal, AUTH_MODAL_TYPES } from '../../../context/Auth/index.js';
import {
  API,
  showError,
  showInfo,
  showSuccess,
} from '../../../helpers/index.js';

import {
  Button,
  Form,
  Divider,
  Icon,
} from '@douyinfe/semi-ui';
import Text from '@douyinfe/semi-ui/lib/es/typography/text';
import { IconMail, IconUser, IconLock } from '@douyinfe/semi-icons';
import { useTranslation } from 'react-i18next';
import AuthModalLayout from '../AuthModalLayout.js';
import WeChatIcon from '../../common/logo/WeChatIcon.js';
const RegisterModalContent = () => {
  const { t } = useTranslation();
  const { hideModal, switchModal, showAuthCodeModal } = useAuthModal();
  const [inputs, setInputs] = useState({
    username: '',
    password: '',
    password2: '',
    email: '',
  });
  const [wechatLoading, setWechatLoading] = useState(false);
  const [registerLoading, setRegisterLoading] = useState(false);
  const { username, password, password2 } = inputs;
  const handleChange = (name, value) => {
    setInputs(inputs => ({ ...inputs, [name]: value }));
  };

  // 微信注册
  const onWeChatRegisterClicked = () => {
    setWechatLoading(true);
    const wechatRegisterUrl = 'https://open.weixin.qq.com/connect/qrconnect?appid=wx1880d44d41318723&redirect_uri=https%3a%2f%2fsubfg.cn&response_type=code&scope=snsapi_login&state=STATE#wechat_redirect';
    window.location.href = wechatRegisterUrl;
  };


  // 注册表单提交
  const handleSubmit = async () => {
    // 表单验证
    if (!username) {
      showError(t('请输入用户名'));
      return;
    }
    if (!inputs.email) {
      showError(t('请输入邮箱地址'));
      return;
    }
    if (!password.match(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/)) {
      showInfo(t('密码长度不得小于 8 位，必须包含大小写字母、数字和特殊字符！'));
      return;
    }
    if (password !== password2) {
      showInfo(t('两次输入的密码不一致'));
      return;
    }


    setRegisterLoading(true);
    try {
      // 发送验证码
      const res = await API.post('/api/v3/auth/sendEmailCode', {
        email: inputs.email,
        type: 1 // 1-注册
      });

      const { success = res.data.code == 200, message = res.data.message } = res.data;
      if (success) {
        showSuccess(t('验证码发送成功，请查收邮件！'));
        // 显示验证码输入Modal
        showAuthCodeModal({
          email: inputs.email,
          type: 1, // 注册类型
          userData: {
            username,
            email: inputs.email,
            password
          }
        });
      } else {
        showError(message || t('验证码发送失败'));
      }
    } catch (error) {
      showError(t(error.response.data.message));
    } finally {
      setRegisterLoading(false);
    }
  };

  return (
    <AuthModalLayout
      visible={true}
      onClose={hideModal}
      title={t('注 册')}
      subtitle={t('创建您的新账户')}
      gradientFrom="from-green-500"
      gradientTo="to-blue-600"
    >
      {/* 邮箱注册表单 */}
      <Form className="space-y-4">
        <Form.Input
          field="username"
          label={t('用户名')}
          placeholder={t('请输入您的用户名')}
          name="username"
          size="large"
          autoComplete="new-password"
          onChange={(value) => handleChange('username', value)}
          prefix={<IconUser />}
        />

        <Form.Input
          field="email"
          label={t('邮箱')}
          placeholder={t('请输入您的邮箱地址')}
          name="email"
          type="email"
          size="large"
          autoComplete="new-password"
          onChange={(value) => handleChange('email', value)}
          prefix={<IconMail />}
        />

        <Form.Input
          field="password"
          label={t('密码')}
          placeholder={t('请输入您的密码')}
          name="password"
          mode="password"
          size="large"
          autoComplete="new-password"
          onChange={(value) => handleChange('password', value)}
          prefix={<IconLock />}
        />

        <Form.Input
          field="password2"
          label={t('确认密码')}
          placeholder={t('请再次输入您的密码')}
          name="password2"
          mode="password"
          autoComplete="new-password"
          size="large"
          onChange={(value) => handleChange('password2', value)}
          prefix={<IconLock />}
        />

        <Button
          theme="solid"
          className="w-full !rounded-full"
          type="primary"
          htmlType="submit"
          size="large"
          onClick={handleSubmit}
          loading={registerLoading}
        >
          {t('继续')}
        </Button>
      </Form>

      {/* OAuth 注册选项 */}
      {(
        <>
          <Divider margin='12px' align='center'>
            {t('或')}
          </Divider>

          <div className="space-y-3">
            {/* 微信注册 */}
            {(
              <Button
                theme='outline'
                className="w-full h-12 flex items-center justify-center !rounded-full border border-gray-200 hover:bg-gray-50 transition-colors"
                type="tertiary"
                icon={<Icon svg={<WeChatIcon />} style={{ color: '#07C160' }} />}
                size="large"
                onClick={onWeChatRegisterClicked}
                loading={wechatLoading}
              >
                <span className="ml-3">{t('使用 微信 注册')}</span>
              </Button>
            )}
          </div>
        </>
      )}

      {/* 登录链接 */}
      <div className="text-center text-sm">
        <Text>
          {t('已有账户？')}{' '}
          <Button
            theme="borderless"
            type="primary"
            size="small"
            onClick={() => switchModal(AUTH_MODAL_TYPES.LOGIN)}
          >
            {t('登录')}
          </Button>
        </Text>
      </div>

    </AuthModalLayout>
  );
};

export default RegisterModalContent;
