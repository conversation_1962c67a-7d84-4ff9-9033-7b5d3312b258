import React, { useContext, useState, useEffect } from 'react';
import { UserContext } from '../../../context/User/index.js';
import { useAuthModal } from '../../../context/Auth/index.js';
import {
  API,
  showError,
  showSuccess,
} from '../../../helpers/index.js';
import {
  Button,
  Form,
  Upload,
  Avatar,
  Tooltip
} from '@douyinfe/semi-ui';

import { IconUser, IconCamera } from '@douyinfe/semi-icons';
import { useTranslation } from 'react-i18next';
import AuthModalLayout from '../AuthModalLayout.js';

const EditProfileModalContent = () => {
  const { t } = useTranslation();
  const [userState, userDispatch] = useContext(UserContext);
  const { hideModal } = useAuthModal();

  const [formData, setFormData] = useState({
    userName: '',
    avatarUrl: '',
    motto: ''
  });
  const [loading, setLoading] = useState(false);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [formApi, setFormApi] = useState(null);

  const { userName, avatarUrl, motto } = formData;

  // 初始化表单数据
  useEffect(() => {
    if (userState?.user) {
      const newFormData = {
        userName: userState.user.userName || '',
        avatarUrl: userState.user.avatarUrl || '',
        motto: userState.user.motto || ''
      };

      setFormData(newFormData);
    }
  }, [userState?.user]);

  useEffect(() => {
  }, [formData, userName, avatarUrl, motto]);

  /**
   * @description 获取用户信息
   */
  const getUserData = async () => {
    try {
      const res = await API.get('/api/v3/user/getUserInfo');
      const { success = res.data.code == 200, data } = res.data;
      if (success) {
        userDispatch({ type: 'login', payload: data });
      }
    } catch (error) {
      showError(t(error.response?.data?.message || '获取用户信息失败'));
    }
  };

  /**
   * 处理表单字段变化
   * @param {string} field - 字段名
   * @param {string} value - 字段值
   */
  const handleFieldChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  /**
   * 获取头像显示文本
   */
  const getAvatarText = () => {
    const name = userName || userState?.user?.userName || '';
    if (name && name.length > 0) {
      return name.slice(0, 2).toUpperCase();
    }
    return 'NA';
  };

  /**
   * 处理头像上传
   */
  const handleAvatarUpload = async (fileList) => {
    if (fileList.length === 0) return;

    const file = fileList[0].fileInstance;
    if (!file) return;

    // 验证文件类型 只接受jpg，png格式的图片
    if (file.type != 'image/jpeg' && file.type != 'image/png') {
      console.log(file.type);
      showError(t('请上传图片文件,只支持 JPG、PNG 格式'));
      return;
    }

    // 验证文件大小 (2MB)
    if (file.size > 2 * 1024 * 1024) {
      showError(t('图片大小不能超过2MB'));
      return;
    }

    setUploadLoading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);
      const res = await API.post('/api/v3/file/upload/avatar', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const { success = res.data.code == 200, data, message = res.data.message } = res.data;
      if (success) {

        // 更新用户数据
        await getUserData();

        showSuccess(t('头像上传成功！'));
      } else {
        showError(message || t('头像上传失败'));
      }
    } catch (error) {
      showError(t(error.response?.data?.message || '头像上传失败'));
    } finally {
      setUploadLoading(false);
    }
  };

  const validateForm = (values) => {
    if (!values.userName || !values.userName.trim()) {
      showError(t('请输入用户名'));
      return false;
    }

    if (values.userName.trim().length < 2) {
      showError(t('用户名至少需要2个字符'));
      return false;
    }

    if (values.userName.trim().length > 20) {
      showError(t('用户名不能超过20个字符'));
      return false;
    }

    if (values.motto && values.motto.length > 100) {
      showError(t('个人简介不能超过100个字符'));
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!formApi) {
      showError(t('表单未准备就绪'));
      return;
    }

    const values = formApi.getValues();
    console.log('Form values:', values);

    if (!validateForm(values)) {
      return;
    }

    setLoading(true);
    try {
      const res = await API.post('/api/v3/user/updateUserInfo', {
        userName: values.userName.trim(),
        avatarUrl: avatarUrl || null,
        motto: values.motto ? values.motto.trim() : null
      });

      const { success = res.data.code == 200, message = res.data.message } = res.data;
      if (success) {
        showSuccess(t('个人信息更新成功！'));
        // 更新用户信息
        userDispatch({
          type: 'login',
          payload: {
            ...userState.user,
            userName: values.userName.trim(),
            avatarUrl: avatarUrl || null,
            motto: values.motto ? values.motto.trim() : null
          }
        });
        hideModal();
      } else {
        showError(message || t('个人信息更新失败'));
      }
    } catch (error) {
      showError(t(error.response?.data?.message || '个人信息更新失败'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthModalLayout
      visible={true}
      onClose={hideModal}
      title={t('编辑个人信息')}
      subtitle={t('完善您的个人资料，让其他用户更好地了解您')}
      gradientFrom="from-purple-500"
      gradientTo="to-pink-600"
    >
      {/* 个人信息编辑表单 */}
      <Form
        className="space-y-6"
        initValues={{
          userName: userName,
          motto: motto
        }}
        key={`${userName}-${motto}`} // 强制重新渲染当数据变化时
        getFormApi={(api) => setFormApi(api)}
      >
        {/* 头像上传 */}
        <div className="space-y-3">

          <div className="flex items-center justify-center gap-2 flex-col">
            <Avatar
              size="large"
              src={avatarUrl}
              shape='circle'
              className="flex-shrink-0 mb-2"
              border={{ color: 'blue' }}
            >
              {!avatarUrl && getAvatarText()}
            </Avatar>
            <Upload
              action=""
              accept="image/*"
              limit={1}
              uploadTrigger="custom"
              showUploadList={false}
              beforeUpload={() => false}
              onChange={({ fileList }) => handleAvatarUpload(fileList)}
              className="flex-1"
            >

              <Tooltip content={t('支持 JPG、PNG 格式，文件大小不超过 2MB')}>
                <Button
                  theme="outline"
                  type="primary"
                  icon={<IconCamera />}
                  loading={uploadLoading}
                  className="!rounded-lg"
                >
                  {uploadLoading ? t('上传中...') : t('更换头像')}
                </Button>
              </Tooltip>
            </Upload>
          </div>

        </div>

        {/* 用户名 */}
        <Form.Input
          field="userName"
          label={t('用户名')}
          placeholder={t('请输入用户名')}
          name="userName"
          size="large"
          prefix={<IconUser />}
          maxLength={20}
          showClear
          onChange={(value) => handleFieldChange('userName', value)}
        />

        {/* 个人简介 */}
        <Form.TextArea
          field="motto"
          label={t('个人简介')}
          placeholder={t('介绍一下自己吧...')}
          name="motto"
          maxLength={100}
          showClear
          autosize={{ minRows: 3, maxRows: 5 }}
          onChange={(value) => handleFieldChange('motto', value)}
        />

        <Button
          theme="solid"
          className="w-full !rounded-full"
          type="primary"
          size="large"
          onClick={handleSubmit}
          loading={loading}
          disabled={loading}
        >
          {t('保存修改')}
        </Button>
      </Form>

      {/* 返回链接 */}
      <Button
        theme="borderless"
        type="tertiary"
        size="large"
        className="w-full !rounded-full !mt-2"
        onClick={hideModal}
      >
        {t('取消')}
      </Button>
    </AuthModalLayout >
  );
};

export default EditProfileModalContent;
