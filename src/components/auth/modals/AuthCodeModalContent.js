import React, { useContext, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { UserContext } from '../../../context/User/index.js';
import { useAuthModal, AUTH_MODAL_TYPES } from '../../../context/Auth/index.js';
import {
  API,
  showError,
  showInfo,
  showSuccess,
  updateAPI,
  setUserData,
  setToken,
} from '../../../helpers/index.js';
import {
  Button,
  PinCode,
} from '@douyinfe/semi-ui';
import Text from '@douyinfe/semi-ui/lib/es/typography/text';
import { useTranslation } from 'react-i18next';
import AuthModalLayout from '../AuthModalLayout.js';
const AuthCodeModalContent = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [userState, userDispatch] = useContext(UserContext);
  const { hideModal, switchModal, modalData, returnUrl, showSetNewPasswordModal } = useAuthModal();

  // 从modalData获取传递的数据
  const {
    email = '',
    type = 1, // 1-注册, 2-登录, 3-忘记密码, 4-换绑邮箱 5-修改密码
    userData = {} // 注册时的用户数据
  } = modalData || {};

  const [code, setCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  // 倒计时逻辑
  useEffect(() => {
    let timer;
    if (countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  // 如果没有邮箱信息，返回注册页面
  useEffect(() => {
    if (!email) {
      switchModal(AUTH_MODAL_TYPES.REGISTER);
    }
  }, [email, switchModal]);

  /**
   * @description 获取验证码类型的显示文本
   */
  const getTypeText = () => {
    switch (type) {
      case 1: return t('注册');
      case 2: return t('登录');
      case 3: return t('密码重置');
      case 4: return t('换绑邮箱');
      default: return t('验证');
    }
  };

  /**
   * @description 重发验证码
   */
  const handleResendCode = async () => {
    if (countdown > 0) return;

    setResendLoading(true);
    try {
      const res = await API.post('/api/v3/auth/sendEmailCode', {
        email,
        type
      });
      const { success = res.data.code == 200, message = res.data.message } = res.data;
      if (success) {
        showSuccess(t('验证码发送成功！'));
        setCountdown(60); // 60秒倒计时
      } else {
        showError(message || t('验证码发送失败'));
      }
    } catch (error) {
      showError(t(error.response.data.message));
    } finally {
      setResendLoading(false);
    }
  };

  /**
   * @description 提交验证码
   */
  const handleSubmit = async () => {
    if (!code || code.length < 6) {
      showError(t('请输入完整的验证码'));
      return;
    }


    setLoading(true);
    try {
      if (type === 1) {
        // 注册流程
        const res = await API.post('/api/v3/auth/register', {
          userName: userData.username,
          email: userData.email,
          password: userData.password,
          code
        });
        const { success = res.data.code == 200, data, message = res.data.message } = res.data;
        if (success) {
          showSuccess(t('注册成功！'));
          switchModal(AUTH_MODAL_TYPES.LOGIN);
        } else {
          showError(message || t('注册失败'));
        }
      } else if (type === 3) {
        // 密码重置流程 - 验证码验证成功后跳转到设置新密码页面
        // 这里我们不需要调用API验证验证码，直接跳转到设置新密码页面
        // 验证码的验证将在设置新密码时一起进行
        showSetNewPasswordModal({
          email,
          emailCode: code
        });
      } else {
        // 其他类型的验证码处理
        showInfo(t('功能开发中...'));
      }
    } catch (error) {
      showError(t(error.response.data.message));
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthModalLayout
      visible={true}
      onClose={hideModal}
      title={t('输入验证码')}
      subtitle={
        <>
          {t('我们已向')} <span className="font-medium text-gray-700">{email}</span> {t('发送了验证码')}
        </>
      }
      gradientFrom="from-indigo-500"
      gradientTo="to-purple-600"
    >
      {/* 验证码输入 */}
      <div className="flex justify-center">
        <PinCode
          size="large"
          count={6}
          onChange={(value) => setCode(value)}
          onComplete={(value) => setCode(value)}
          autoFocus
        />
      </div>

      {/* 提交按钮 */}
      <Button
        theme="solid"
        className="w-full !rounded-full"
        type="primary"
        size="large"
        onClick={handleSubmit}
        loading={loading}
        disabled={!code || code.length < 6}
      >
        {type === 1 ? t('完成注册') : type === 3 ? t('验证并继续') : t('继续')}
      </Button>


      {/* 返回链接 */}
      <div className="text-center text-sm">
        <Text>
          <Button
            theme="borderless"
            type="tertiary"
            size="small"
            onClick={() => switchModal(
              type === 1 ? AUTH_MODAL_TYPES.REGISTER :
                type === 3 ? AUTH_MODAL_TYPES.RESET_PASSWORD :
                  AUTH_MODAL_TYPES.LOGIN
            )}
          >
            {t('返回上一步')}
          </Button>
        </Text>
      </div>

    </AuthModalLayout>
  );
};

export default AuthCodeModalContent;
