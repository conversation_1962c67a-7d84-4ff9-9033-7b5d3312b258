import React, { useEffect, useState } from 'react';
import { useAuthModal, AUTH_MODAL_TYPES } from '../../../context/Auth/index.js';
import {
  API,
  showError,
  showInfo,
  showSuccess,
} from '../../../helpers/index.js';

import {
  Button,
  Form,
} from '@douyinfe/semi-ui';
import Text from '@douyinfe/semi-ui/lib/es/typography/text';
import { IconMail } from '@douyinfe/semi-icons';
import { useTranslation } from 'react-i18next';
import AuthModalLayout from '../AuthModalLayout.js';

const ResetPasswordModalContent = () => {
  const { t } = useTranslation();
  const { hideModal, switchModal, showSetNewPasswordModal } = useAuthModal();
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [disableButton, setDisableButton] = useState(false);
  const [countdown, setCountdown] = useState(0);
  // 倒计时逻辑
  useEffect(() => {
    let timer;
    if (countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
    } else {
      setDisableButton(false);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  const handleChange = (value) => {
    setEmail(value);
  };

  const handleSubmit = async () => {
    if (!email) {
      showError(t('请输入邮箱地址'));
      return;
    }


    setLoading(true);
    try {
      const res = await API.post('/api/v3/auth/sendEmailCode', {
        email,
        type: 3 // 3-忘记密码
      });

      const { success = res.data.code == 200, message = res.data.message } = res.data;
      if (success) {
        showSuccess(t('验证码发送成功，请查收邮件！'));
        // 直接跳转到设置新密码页面
        showSetNewPasswordModal({
          email
        });
      } else {
        showError(message || t('验证码发送失败'));
      }
    } catch (error) {
      console.log(error);
      showError(t(error.response.data.message));
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthModalLayout
      visible={true}
      onClose={hideModal}
      title={t('密码重置')}
      subtitle={t('输入您的邮箱地址,然后使用验证码重置密码')}
      gradientFrom="from-purple-500"
      gradientTo="to-pink-600"
    >
      {/* 密码重置表单 */}
      <Form className="space-y-4">
        <Form.Input
          field="email"
          label={t('邮箱')}
          placeholder={t('请输入您的邮箱地址')}
          name="email"
          autoComplete="new-password"
          size="large"
          value={email}
          onChange={handleChange}
          prefix={<IconMail />}
        />

        <Button
          theme="solid"
          className="w-full !rounded-full"
          type="primary"
          htmlType="submit"
          size="large"
          onClick={handleSubmit}
          loading={loading}
          disabled={disableButton}
        >
          {disableButton ? `${t('重试')} (${countdown})` : t('提交')}
        </Button>
      </Form>

      {/* 返回登录链接 */}
      <div className="text-center text-sm">
        <Text>
          {t('想起来了？')}{' '}
          <Button
            theme="borderless"
            type="primary"
            size="small"
            onClick={() => switchModal(AUTH_MODAL_TYPES.LOGIN)}
          >
            {t('登录')}
          </Button>
        </Text>
      </div>
    </AuthModalLayout>
  );
};

export default ResetPasswordModalContent;
