import React, { useContext, useState, useEffect } from 'react';
import { UserContext } from '../../../context/User/index.js';
import { useAuthModal } from '../../../context/Auth/index.js';
import {
  API,
  showError,
  showSuccess,
} from '../../../helpers/index.js';
import {
  Button,
  Form,
  PinCode,
} from '@douyinfe/semi-ui';
import Text from '@douyinfe/semi-ui/lib/es/typography/text';
import { IconMail } from '@douyinfe/semi-icons';
import { useTranslation } from 'react-i18next';
import AuthModalLayout from '../AuthModalLayout.js';

const ChangeEmailModalContent = () => {
  const { t } = useTranslation();
  const [userState, userDispatch] = useContext(UserContext);
  const { hideModal } = useAuthModal();

  const [formData, setFormData] = useState({
    newEmail: '',
    emailCode: ''
  });
  const [loading, setLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [codeSent, setCodeSent] = useState(false);

  const { newEmail, emailCode } = formData;

  // 倒计时逻辑
  useEffect(() => {
    let timer;
    if (countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  /**
   * 处理表单字段变化
   * @param {string} field - 字段名
   * @param {string} value - 字段值
   */
  const handleFieldChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  /**
   * 发送验证码到新邮箱
   */
  const handleSendCode = async () => {
    if (!newEmail) {
      showError(t('请输入新邮箱地址'));
      return;
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(newEmail)) {
      showError(t('请输入有效的邮箱地址'));
      return;
    }

    if (newEmail === userState?.user?.email) {
      showError(t('新邮箱不能与当前邮箱相同'));
      return;
    }

    setResendLoading(true);
    try {
      const res = await API.post('/api/v3/auth/sendEmailCode', {
        email: newEmail,
        type: 4 // 4-换绑邮箱
      });

      const { success = res.data.code == 200, message = res.data.message } = res.data;
      if (success) {
        showSuccess(t('验证码发送成功！'));
        setCodeSent(true);
        setCountdown(60); // 60秒倒计时
      } else {
        showError(message || t('验证码发送失败'));
      }
    } catch (error) {
      showError(t(error.response.data.message));
    } finally {
      setResendLoading(false);
    }
  };

  const validateForm = () => {
    if (!newEmail) {
      showError(t('请输入新邮箱地址'));
      return false;
    }

    if (!emailCode) {
      showError(t('请输入验证码'));
      return false;
    }

    if (emailCode.length !== 6) {
      showError(t('请输入完整的验证码'));
      return false;
    }

    return true;
  };
  async function logout() {
    await API.post('/api/v3/auth/logout');
    userDispatch({ type: 'logout' });
    localStorage.removeItem('user');
    localStorage.removeItem('token');

  }
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const res = await API.post('/api/v3/user/changeEmail', {
        newEmail,
        newEmailCode: emailCode
      });

      const { success = res.data.code == 200, message = res.data.message } = res.data;
      if (success) {
        showSuccess(t('邮箱换绑成功！'));
        // 推出登录
        await logout();
        switchModal(AUTH_MODAL_TYPES.LOGIN);
      } else {
        showError(message || t('邮箱换绑失败'));
      }
    } catch (error) {
      showError(t(error.response.data.message));
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthModalLayout
      visible={true}
      onClose={hideModal}
      title={t('换绑邮箱')}
      subtitle={
        <>
          {t('当前邮箱：')} <span className="font-medium text-gray-700">{userState?.user?.email || t('未绑定')}</span>
          <br />
          {t('请输入新邮箱地址并验证')}
        </>
      }
      gradientFrom="from-blue-500"
      gradientTo="to-cyan-600"
    >
      {/* 邮箱换绑表单 */}
      <Form className="space-y-4">
        {/* 新邮箱输入 */}
        <div className="space-y-2">
          <Form.Input
            field="newEmail"
            label={t('新邮箱地址')}
            placeholder={t('请输入新的邮箱地址')}
            name="newEmail"
            type="email"
            size="large"
            value={newEmail}
            onChange={(value) => handleFieldChange('newEmail', value)}
            prefix={<IconMail />}
            disabled={codeSent}
          />

          {!codeSent && (
            <Button
              theme="solid"
              type="primary"
              size="large"
              onClick={handleSendCode}
              loading={resendLoading}
              disabled={!newEmail}
              className="w-full !rounded-full"
            >
              {t('发送验证码')}
            </Button>
          )}
        </div>

        {/* 验证码输入 */}
        {codeSent && (
          <div className="space-y-2">
            <Text className="text-sm font-medium text-gray-700">{t('邮箱验证码')}</Text>
            <div className="flex justify-center">
              <PinCode
                size="large"
                count={6}
                value={emailCode}
                onChange={(value) => handleFieldChange('emailCode', value)}
                onComplete={(value) => handleFieldChange('emailCode', value)}
                autoFocus
              />
            </div>

            {/* 重发验证码 */}
            <div className="text-center">
              <Text className="text-gray-500 text-sm">
                {t('没有收到验证码？')}
              </Text>
              <Button
                theme="borderless"
                type="primary"
                size="small"
                onClick={handleSendCode}
                loading={resendLoading}
                disabled={countdown > 0}
                className="ml-2"
              >
                {countdown > 0 ? `${countdown}s ${t('后重发')}` : t('重新发送')}
              </Button>
            </div>

            <Button
              theme="solid"
              className="w-full !rounded-full"
              type="primary"
              size="large"
              onClick={handleSubmit}
              loading={loading}
              disabled={!emailCode || emailCode.length !== 6}
            >
              {t('确认换绑')}
            </Button>
          </div>
        )}
      </Form>

      {/* 返回链接 */}
      <div className="text-center text-sm">
        <Text>
          <Button
            theme="borderless"
            type="tertiary"
            size="small"
            onClick={hideModal}
          >
            {t('取消')}
          </Button>
        </Text>
      </div>
    </AuthModalLayout>
  );
};

export default ChangeEmailModalContent;
