import React, { useEffect } from 'react';
import { useAuthModal } from '../../context/Auth/index.js';

/**
 * 私有路由包装器组件
 * 用于处理需要登录才能访问的页面
 * 如果用户未登录，会自动显示登录Modal
 */
const PrivateRouteWrapper = ({ children }) => {
  const { showLoginModal } = useAuthModal();
  useEffect(() => {
    const isAuthenticated = localStorage.getItem('token');
    if (!isAuthenticated) {
      // 如果用户未登录，显示登录Modal
      // 传入当前路径，登录成功后可以返回到这个页面
      showLoginModal(window.location.pathname);
    }
  }, [showLoginModal]);

  // 始终渲染子组件，让用户可以看到页面内容
  // 如果未登录，会显示登录Modal覆盖在页面上
  return children;
};

export default PrivateRouteWrapper;
