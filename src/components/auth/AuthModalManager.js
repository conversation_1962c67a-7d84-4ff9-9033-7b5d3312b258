import React, { useEffect } from 'react';
import { useAuthModal, AUTH_MODAL_TYPES } from '../../context/Auth/index.js';
import { setGlobalAuthModalHandler } from '../../helpers/utils.js';
import LoginModalContent from './modals/LoginModalContent.js';
import RegisterModalContent from './modals/RegisterModalContent.js';
import ResetPasswordModalContent from './modals/ResetPasswordModalContent.js';
import AuthCodeModalContent from './modals/AuthCodeModalContent.js';
import SetNewPasswordModalContent from './modals/SetNewPasswordModalContent.js';
import ChangePasswordModalContent from './modals/ChangePasswordModalContent.js';
import ChangeEmailModalContent from './modals/ChangeEmailModalContent.js';
import EditProfileModalContent from './modals/EditProfileModalContent.js';

/**
 * 认证Modal管理器组件
 * 根据当前的Modal类型渲染对应的Modal内容
 */
const AuthModalManager = () => {
  const { isVisible, modalType, showLoginModal } = useAuthModal();

  // 设置全局认证Modal处理器
  useEffect(() => {
    setGlobalAuthModalHandler(showLoginModal);
  }, [showLoginModal]);

  if (!isVisible) {
    return null;
  }

  const renderModalContent = () => {
    switch (modalType) {
      case AUTH_MODAL_TYPES.LOGIN:
        return <LoginModalContent />;
      case AUTH_MODAL_TYPES.REGISTER:
        return <RegisterModalContent />;
      case AUTH_MODAL_TYPES.RESET_PASSWORD:
        return <ResetPasswordModalContent />;
      case AUTH_MODAL_TYPES.AUTH_CODE:
        return <AuthCodeModalContent />;
      case AUTH_MODAL_TYPES.SET_NEW_PASSWORD:
        return <SetNewPasswordModalContent />;
      case AUTH_MODAL_TYPES.CHANGE_PASSWORD:
        return <ChangePasswordModalContent />;
      case AUTH_MODAL_TYPES.CHANGE_EMAIL:
        return <ChangeEmailModalContent />;
      case AUTH_MODAL_TYPES.EDIT_PROFILE:
        return <EditProfileModalContent />;
      default:
        return null;
    }
  };

  return renderModalContent();
};

export default AuthModalManager;
