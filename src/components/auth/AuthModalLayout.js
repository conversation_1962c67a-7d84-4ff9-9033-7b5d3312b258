import React from 'react';
import { Modal, Image } from '@douyinfe/semi-ui';
import Title from '@douyinfe/semi-ui/lib/es/typography/title';
import Text from '@douyinfe/semi-ui/lib/es/typography/text';
import { getLogo, getSystemName } from '../../helpers/index.js';
import './auth-modal.css';

/**
 * 认证Modal布局组件
 * @param {Object} props
 * @param {boolean} props.visible - Modal是否可见
 * @param {function} props.onClose - 关闭Modal的回调
 * @param {string} props.title - Modal标题
 * @param {string} props.subtitle - Modal副标题
 * @param {React.ReactNode} props.children - Modal内容
 * @param {string} props.gradientFrom - 左侧背景渐变起始色
 * @param {string} props.gradientTo - 左侧背景渐变结束色
 * @param {number} props.width - Modal宽度
 * @param {boolean} props.closable - 是否显示关闭按钮
 */
const AuthModalLayout = ({
  visible,
  onClose,
  title,
  subtitle,
  children,
  gradientFrom = 'from-blue-500',
  gradientTo = 'to-purple-600',
  width = 1000,
  closable = true,
}) => {
  const logo = getLogo();
  const systemName = getSystemName();

  return (
    <Modal
      visible={visible}
      onCancel={onClose}
      closable={closable}
      footer={null}
      width={width}
      centered
      bodyStyle={{ padding: 0 }}
      className="auth-modal"
      maskClosable={false}
    >
      <div className="flex h-full">
        {/* 左侧图片区域 */}
        <div className={`hidden md:flex md:w-2/5 bg-gradient-to-br ${gradientFrom} ${gradientTo} relative overflow-hidden`}>
          <Image
            src="/images/auth-bg.jpg"
            alt="Authentication Background"
            width="100%"
            height="100%"
            imgStyle={{
              objectFit: 'cover',
              width: '100%',
              height: '100%',
            }}
            preview={false}
            placeholder={
              <Image
                src="/images/auth-bg.png"
                alt="Authentication Background Placeholder"
                width="100%"
                height="100%"
                preview={false}
                imgStyle={{
                  objectFit: 'cover',
                  width: '100%',
                  height: '100%',
                }}
              />
            }
          />
        </div>

        {/* 右侧表单区域 */}
        <div className="flex-1 md:w-3/5 flex items-center justify-center p-8 bg-white">
          <div className="w-full max-w-md">
            {/* 头部标题 */}
            <div className="text-center mb-8">
              <div className="flex items-center justify-center mb-4 gap-2">
                <img src='/logo-title.svg' alt="Logo" className="h-10 rounded-full" />
              </div>
              <Title heading={2} className="text-gray-800 dark:text-gray-200 mb-2">
                {title}
              </Title>
              {subtitle && (
                <Text className="text-gray-500">
                  {subtitle}
                </Text>
              )}
            </div>

            {/* 表单内容 */}
            <div className="space-y-6">
              {children}
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default AuthModalLayout;
