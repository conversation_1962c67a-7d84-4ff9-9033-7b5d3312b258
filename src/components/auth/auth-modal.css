/* 认证模态框样式 */
.auth-modal .semi-modal-content {
  border-radius: 16px;
  overflow: hidden;
}

.auth-modal .semi-modal-body {
  padding: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .auth-modal {
    width: 95vw !important;
  }
}

/* 图片加载动画 */
/* .auth-bg-placeholder {
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size: 400% 400%;
  animation: gradient 15s ease infinite;
} */

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}