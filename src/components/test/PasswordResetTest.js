import React from 'react';
import { Button } from '@douyinfe/semi-ui';
import { useAuthModal } from '../../context/Auth/index.js';

/**
 * 密码重置功能测试组件
 * 这个组件仅用于开发测试，生产环境中应该移除
 */
const PasswordResetTest = () => {
  const { showResetPasswordModal, showAuthCodeModal, showSetNewPasswordModal } = useAuthModal();

  const testResetPasswordFlow = () => {
    // 测试完整的密码重置流程
    showResetPasswordModal();
  };

  const testAuthCodeStep = () => {
    // 测试验证码输入步骤
    showAuthCodeModal({
      email: '<EMAIL>',
      type: 3, // 密码重置类型
    });
  };

  const testSetNewPasswordStep = () => {
    // 测试设置新密码步骤
    showSetNewPasswordModal({
      email: '<EMAIL>',
      emailCode: '123456',
    });
  };

  return (
    <div className="p-4 space-y-4">
      <h3>密码重置功能测试组件</h3>
      <div className="space-y-2">
        <Button 
          type="primary" 
          onClick={testResetPasswordFlow}
          className="w-full"
        >
          测试完整密码重置流程
        </Button>
        
        <Button 
          type="secondary" 
          onClick={testAuthCodeStep}
          className="w-full"
        >
          测试验证码输入步骤
        </Button>
        
        <Button 
          type="tertiary" 
          onClick={testSetNewPasswordStep}
          className="w-full"
        >
          测试设置新密码步骤
        </Button>
      </div>
      <p className="text-sm text-gray-500">
        注意：这个组件仅用于开发测试，生产环境中应该移除
      </p>
    </div>
  );
};

export default PasswordResetTest;
