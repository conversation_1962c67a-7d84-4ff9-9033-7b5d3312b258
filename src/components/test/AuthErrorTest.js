import React from 'react';
import { Button } from '@douyinfe/semi-ui';
import { showError } from '../../helpers/index.js';

/**
 * 测试组件：用于测试401错误处理和登录Modal调用
 * 这个组件仅用于开发测试，生产环境中应该移除
 */
const AuthErrorTest = () => {

  const testUnauthorizedError = () => {
    // 模拟一个401错误
    const mockError = {
      name: 'AxiosError',
      message: '未授权访问',
      response: {
        status: 401,
        data: {
          message: '登录已过期'
        }
      }
    };

    showError(mockError);
  };

  const test429Error = () => {
    // 模拟一个429错误
    const mockError = {
      name: 'AxiosError',
      message: '请求过于频繁',
      response: {
        status: 429,
        data: {
          message: '请求次数过多'
        }
      }
    };

    showError(mockError);
  };

  return (
    <div className="p-4 space-y-4">
      <h3>认证错误测试组件</h3>
      <div className="space-x-2">
        <Button
          type="primary"
          onClick={testUnauthorizedError}
        >
          测试401错误（应该显示登录Modal）
        </Button>
        <Button
          type="secondary"
          onClick={test429Error}
        >
          测试429错误（应该显示Toast）
        </Button>
      </div>
      <p className="text-sm text-gray-500">
        注意：这个组件仅用于开发测试，生产环境中应该移除
      </p>
    </div>
  );
};

export default AuthErrorTest;
